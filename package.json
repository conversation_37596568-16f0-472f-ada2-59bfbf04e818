{"name": "golf_course_system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "prebuild": "node scripts/gen-version.cjs", "build": "tsc -b && vite build", "build:test": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.3", "axios": "^1.10.0", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "zustand": "^5.0.6"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.11", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/nprogress": "^0.2.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}