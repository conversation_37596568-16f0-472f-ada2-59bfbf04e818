# Antd 5 Table 高度设置完整指南

## 核心概念

在 Antd 5 中，Table 组件的高度设置主要通过以下方式实现：

1. **scroll.y 属性**：控制表格主体的垂直滚动高度
2. **CSS 容器高度**：设置表格容器的高度
3. **Flexbox 布局**：使用弹性布局实现自适应高度

## 方法一：固定高度

```typescript
<Table
  dataSource={data}
  columns={columns}
  scroll={{ y: 400 }} // 固定高度 400px
  pagination={false}
/>
```

## 方法二：百分比高度

```typescript
<div style={{ height: '100vh' }}>
  <Table
    dataSource={data}
    columns={columns}
    scroll={{ y: '80vh' }} // 视口高度的 80%
    pagination={false}
  />
</div>
```

## 方法三：calc() 动态计算

```typescript
<Table
  dataSource={data}
  columns={columns}
  scroll={{ 
    y: 'calc(100vh - 200px)', // 视口高度减去其他元素高度
    x: 'max-content' // 水平滚动
  }}
  pagination={{
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  }}
/>
```

## 方法四：Flexbox 自适应布局

```typescript
const MyComponent = () => {
  return (
    <div className="h-full flex flex-col">
      {/* 头部工具栏 */}
      <div className="tool-bar p-4 bg-white">
        <Button type="primary">新增</Button>
      </div>
      
      {/* 表格容器 - 使用 flex-1 占满剩余空间 */}
      <div className="flex-1 bg-white mx-4 mb-4 overflow-hidden">
        <Table
          dataSource={data}
          columns={columns}
          scroll={{ 
            y: 'calc(100vh - 200px)',
            x: 'max-content'
          }}
          pagination={{
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </div>
    </div>
  );
};
```

## 方法五：使用 CSS 类

```css
/* 在 CSS 文件中定义 */
.full-height-table {
  height: 100%;
}

.full-height-table .ant-table-tbody {
  height: calc(100% - 55px); /* 减去表头高度 */
}
```

```typescript
<div className="full-height-table">
  <Table
    dataSource={data}
    columns={columns}
    scroll={{ y: '100%' }}
  />
</div>
```

## 实际应用示例

### 示例 1：管理页面布局

```typescript
const ManagementPage: React.FC = () => {
  return (
    <div className="h-full w-full flex flex-col">
      {/* 搜索栏 */}
      <div className="search-bar p-4 bg-white">
        <Input placeholder="搜索..." />
        <Button type="primary">搜索</Button>
      </div>

      {/* 工具栏 */}
      <div className="tool-bar p-4 bg-white border-t">
        <Button type="primary" icon={<PlusOutlined />}>
          新增
        </Button>
      </div>

      {/* 表格区域 */}
      <div className="flex-1 bg-white mx-4 mb-4 overflow-hidden">
        <Table
          dataSource={data}
          columns={columns}
          scroll={{ 
            y: 'calc(100vh - 180px)', // 根据实际布局调整
            x: 'max-content'
          }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
        />
      </div>
    </div>
  );
};
```

### 示例 2：Modal 中的表格

```typescript
<Modal
  title="选择数据"
  open={visible}
  width={800}
  height={600}
>
  <div style={{ height: '400px' }}>
    <Table
      dataSource={data}
      columns={columns}
      scroll={{ y: 350 }} // Modal 内容区域高度减去其他元素
      size="small"
    />
  </div>
</Modal>
```

## 常见问题及解决方案

### 1. 表格高度不生效

**问题**：设置了 scroll.y 但表格高度没有变化
**解决**：确保父容器有明确的高度

```typescript
// ❌ 错误：父容器没有高度
<div>
  <Table scroll={{ y: '100%' }} />
</div>

// ✅ 正确：父容器有明确高度
<div style={{ height: '500px' }}>
  <Table scroll={{ y: '100%' }} />
</div>
```

### 2. 分页器被遮挡

**问题**：表格高度设置后，分页器不可见
**解决**：在计算高度时预留分页器空间

```typescript
<Table
  scroll={{ 
    y: 'calc(100vh - 250px)' // 预留分页器高度
  }}
  pagination={{
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  }}
/>
```

### 3. 响应式高度问题

**问题**：在不同屏幕尺寸下高度不合适
**解决**：使用媒体查询或动态计算

```typescript
const useTableHeight = () => {
  const [height, setHeight] = useState('calc(100vh - 200px)');
  
  useEffect(() => {
    const updateHeight = () => {
      const windowHeight = window.innerHeight;
      if (windowHeight < 768) {
        setHeight('calc(100vh - 150px)');
      } else {
        setHeight('calc(100vh - 200px)');
      }
    };
    
    window.addEventListener('resize', updateHeight);
    updateHeight();
    
    return () => window.removeEventListener('resize', updateHeight);
  }, []);
  
  return height;
};
```

## 最佳实践

1. **使用 Flexbox 布局**：推荐使用 flex 布局实现自适应高度
2. **预留空间**：计算高度时要考虑分页器、工具栏等元素
3. **响应式设计**：在不同屏幕尺寸下调整表格高度
4. **性能优化**：对于大数据量，启用虚拟滚动
5. **用户体验**：确保表格在各种情况下都有合适的高度

## 调试技巧

1. **使用浏览器开发者工具**检查元素的实际高度
2. **添加边框**临时查看容器边界
3. **使用 console.log**输出计算后的高度值
4. **检查 CSS 层叠**确保样式没有被覆盖
