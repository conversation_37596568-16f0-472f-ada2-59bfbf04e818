:where(.css-dev-only-do-not-override-19mmhfr).ant-layout-sider{
    background-color: #fff;
}
// .menu-list{
//     .ant-menu-item{
//         display: flex;
//         align-items: center !important;
//         justify-content: center !important;
//     }
//     .ant-menu-submenu-title{
//         display: flex;
//         align-items: center !important;
//         justify-content: center !important;
//     }

// }

// .ant-menu-inline-collapsed{
//     .ant-menu-title-content{
//         display: none !important;
//     }
// }