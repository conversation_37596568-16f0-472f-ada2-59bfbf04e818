@import 'tailwindcss';
@import './assets/scss/antd.css';

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f3f6fb;
}

html {
  font-size: 16px !important;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

* {
  box-sizing: border-box;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.sider-scrollbar::-webkit-scrollbar {
  width: 8px;
}
.sider-scrollbar::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 4px;
}
.sider-scrollbar::-webkit-scrollbar-track {
  background: #f8f8f8;
  border-radius: 4px;
}

:where(
    .css-dev-only-do-not-override-19mmhfr,
    .css-19mmhfr
  ).ant-menu-light.ant-menu-inline
  .ant-menu-sub.ant-menu-inline {
  background-color: #fff;
}

:where(.css-dev-only-do-not-override-19mmhfr).ant-layout{
  background-color: #f3f6fb;
}

@media screen and (min-width: 1728px) {
  html {
    font-size: 16px !important;
  }
}

@media screen and (min-width: 1424px) and (max-width: 1727px) {
  html {
    font-size: 14px !important;
  }
}

@media screen and (min-width: 1192px) and (max-width: 1423px) {
  html {
    font-size: 14px !important;
  }
}

@media screen and (min-width: 960px) and (max-width: 1191px) {
  html {
    font-size: 14px !important;
  }
}

@media screen and (min-width: 769px) and (max-width: 959px) {
  html {
    font-size: 14px !important;
  }
}

@media screen and (min-width: 696px) and (max-width: 768px) {
  html {
    font-size: 14px !important;
  }
}

@media screen and (max-width: 695px) {
  html {
    font-size: 14px !important;
  }
}

.tool-box .tool {
  min-width: 80px;
  padding: 0 12px;
  height: 34px;
  background: #e6ecff;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: 500;
  font-size: 14px;
  color: #6280f5;
  cursor: pointer;
}

