import React, { useEffect } from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import useLayoutStore from '../store/useLayoutStore';
import AppHeader from './Header';
import SiderMenu from './Sider';

const { Content } = Layout;

const AppLayout: React.FC = () => {
  const isMobile = useLayoutStore((state) => state.isMobile);
  const setIsMobile = useLayoutStore((state) => state.setIsMobile);
  const setCollapsed = useLayoutStore((state) => state.setCollapsed);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [setIsMobile, setCollapsed]);

  return (
    <Layout style={{ height: '100vh' }} className="responsive-layout">
      <SiderMenu />
      <Layout>
        <AppHeader />
        <Content
          style={{
            padding: isMobile ? '0.75rem' : '1rem',
            minHeight: 280,
            overflow: 'auto',
          }}
          className="responsive-content"
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
