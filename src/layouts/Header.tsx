import React from 'react';
import { Layout, Button, Avatar, Dropdown, Space } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useNavigate } from 'react-router-dom';
import useUserStore from '../store/useUserStore';
import useLayoutStore from '../store/useLayoutStore';

const { Header } = Layout;

const AppHeader: React.FC = () => {
  const collapsed = useLayoutStore((state) => state.collapsed);
  const setCollapsed = useLayoutStore((state) => state.setCollapsed);
  const userStore = useUserStore();
  const navigate = useNavigate();
  const userName: string = userStore.user?.user_name as string;

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      userStore.logOut();
      navigate('/login');
    }
  };

  return (
    <Header
      style={{
        padding: '0 1rem',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      className="responsive-header"
    >
      <Button
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={() => setCollapsed(!collapsed)}
        style={{
          fontSize: '1rem',
          width: '2.5rem',
          height: '2.5rem',
        }}
        className="responsive-toggle-btn"
      />
      <div className="flex items-center space-x-4">
        <div className="d-none d-md-flex items-center space-x-2">
          <Dropdown menu={{ items: userMenuItems, onClick: handleMenuClick }}>
            <Space className="cursor-pointer">
              <Avatar size="small" icon={<UserOutlined />} />
              <span className="text-sm">{userName}</span>
            </Space>
          </Dropdown>
        </div>
      </div>
    </Header>
  );
};

export default AppHeader;
