/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo, useCallback, useState } from 'react';
import './Sider.scss';
import { Layout, Menu } from 'antd';
import type { MenuProps } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import useLayoutStore from '../store/useLayoutStore';
import useUserStore from '../store/useUserStore';

// 定义菜单项类型
interface MenuItem {
  path?: string;
  uri?: string;
  name: string;
  sub?: MenuItem[];
}

const { Sider } = Layout;

const SiderMenuComponent: React.FC = () => {
  const collapsed = useLayoutStore((state) => state.collapsed);
  const setCollapsed = useLayoutStore((state) => state.setCollapsed);
  const navigate = useNavigate();
  const location = useLocation();
  const userStore = useUserStore();
  const menuList = userStore.menuList;
  const clientInfo: any = userStore.clientInfo;

  // 状态管理展开的菜单项，从localStorage读取初始状态
  const [openKeys, setOpenKeys] = useState<string[]>(() => {
    const saved = localStorage.getItem('menu-open-keys');
    return saved ? JSON.parse(saved) : [];
  });

  // 生成图标路径的函数
  const getIconSrc = useCallback(
    (url: string) => {
      console.log(url, 'url');
      // 如果url为空或undefined，返回默认图标
      if (!url) {
        return `/image/sidebaricon/default.svg`;
      }

      // 直接根据当前路径判断后缀
      let pathurl = '';
      pathurl =
        location.pathname === '/home'
          ? location.pathname
          : location.pathname.match(/^\/([^_]+)_/)?.[1] || '';

      // 从url中提取图标名称，支持多种格式
      let iconName = 'default';

      // 移除开头的斜杠（如果有）
      const cleanUrl = url.startsWith('/') ? url.slice(1) : url;

      // 尝试多种方式提取图标名称
      if (cleanUrl.includes('_')) {
        // 如果包含下划线，取第一部分
        iconName = cleanUrl.split('_')[0];
      } else if (cleanUrl.includes('/')) {
        // 如果包含斜杠，取第一部分
        iconName = cleanUrl.split('/')[0];
      } else {
        // 否则直接使用整个字符串
        iconName = cleanUrl;
      }

      // 如果提取的图标名称为空，使用默认值
      if (!iconName) {
        iconName = 'default';
      }

      const suffix = pathurl.indexOf(iconName) !== -1 ? '_selectd' : '';
      return `/image/sidebaricon/${iconName}${suffix}.svg`;
    },
    [location.pathname]
  );

  // 图标组件，带错误处理
  const IconComponent = useCallback(
    ({ src, alt }: { src: string; alt: string }) => (
      <img
        src={src}
        alt={alt}
        className="w-[20px] h-[20px] mr-1"
        onError={(e) => {
          // 如果图标加载失败，使用默认图标
          const target = e.target as HTMLImageElement;
          target.src = '/image/sidebaricon/default.svg';
        }}
      />
    ),
    []
  );

  // 使用useCallback缓存导航函数
  const handleMenuClick = useCallback(
    (path: string) => {
      console.log(path, 'path');
      navigate(path);
    },
    [navigate]
  );

  // 处理菜单展开/收起，并保存到localStorage
  const handleOpenChange = useCallback((keys: string[]) => {
    // 只保留最后一个展开的菜单，实现手风琴效果
    const latestOpenKey = keys.length > 0 ? [keys[keys.length - 1]] : [];
    setOpenKeys(latestOpenKey);
    localStorage.setItem('menu-open-keys', JSON.stringify(latestOpenKey));
  }, []);

  // 使用useMemo缓存menuItems，避免每次重新创建
  const menuItems: MenuProps['items'] = useMemo(() => {
    // 确保menuList存在且为数组
    if (!Array.isArray(menuList)) {
      return [
        {
          key: '/home',
          icon: <IconComponent src={getIconSrc('/home')} alt="首页" />,
          label: '首页',
          onClick: () => navigate('/home'),
        },
      ];
    }

    // 处理动态菜单项
    const dynamicMenuItems = menuList.map((item: MenuItem, index: number) => {
      const key = item.uri || index;
      const label = item.name || '';

      // 检查是否有子菜单
      if (item.sub && Array.isArray(item.sub) && item.sub.length > 0) {
        // 如果只有一个子菜单，直接显示子菜单项
        if (item.sub.length === 1) {
          const child = item.sub[0];
          return {
            key: child.uri || '',
            icon: (
              <IconComponent
                src={getIconSrc(child.uri || '')}
                alt={child.name}
              />
            ),
            label: child.name || '',
            onClick: () => handleMenuClick(child.uri || ''),
          };
        } else {
          // 如果有多个子菜单，创建子菜单结构
          return {
            key,
            icon: (
              <IconComponent
                src={getIconSrc(item.sub[0].uri || '')}
                alt={item.name}
              />
            ),
            label,
            children: item.sub.map((child: MenuItem) => ({
              key: child.uri || '',
              label: child.name || '',
              onClick: () => handleMenuClick(child.uri || ''),
            })),
          };
        }
      } else {
        // 没有子菜单的情况
        return {
          key: item.uri || '',
          label,
          onClick: () => handleMenuClick(item.uri || ''),
        };
      }
    });

    // 添加首页菜单项到开头
    return [
      {
        key: '/home',
        icon: <IconComponent src={getIconSrc('/home')} alt="首页" />,
        label: '首页',
        onClick: () => navigate('/home'),
      },
      ...dynamicMenuItems,
    ];
  }, [handleMenuClick, menuList, navigate, getIconSrc, IconComponent]);

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className="responsive-sider !bg-white"
      breakpoint="lg"
      onBreakpoint={(broken) => {
        if (broken) {
          setCollapsed(true);
        }
      }}
    >
      <div className="demo-logo-vertical p-4 text-center">
        <div
          className={` transition-all duration-300 w-full flex items-center justify-center ${
            collapsed ? 'text-sm' : 'text-lg'
          }`}
        >
          {collapsed ? (
            <img src={clientInfo.image} className="w-10 h-10" />
          ) : (
            <div className="flex items-center justify-center">
              <img src={clientInfo.image} className="w-10 h-10" />
              <span className="text-[14px] text-[#6180f5] font-bold ml-2 whitespace-nowrap">
                {clientInfo.client_name as string}
              </span>
            </div>
          )}
        </div>
      </div>
      {/* 菜单加滚动条包裹层 */}
      <div
        className="sider-scrollbar"
        style={{ height: 'calc(100vh - 64px)', overflow: 'auto' }}
      >
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          defaultOpenKeys={[]}
          items={menuItems}
          className="responsive-menu"
          style={{ borderRight: 'none' }}
        />
      </div>
    </Sider>
  );
};

const SiderMenu = React.memo(SiderMenuComponent);

export default SiderMenu;
