/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { Layout, Menu } from 'antd';
import type { MenuProps } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import useLayoutStore from '../store/useLayoutStore';
import useUserStore from '../store/useUserStore';
import '../assets/scss/layout/sider.scss';

// 定义菜单项类型接口
interface MenuItem {
  id: string;
  uri?: string;
  name: string;
  sub?: MenuItem[];
}

const { Sider } = Layout;

const AppSider: React.FC = () => {
  const { collapsed, setCollapsed } = useLayoutStore();
  const { menuList, clientInfo } = useUserStore();
  const navigate = useNavigate();
  const location = useLocation();

  // 状态管理
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 递归查找菜单项
  const findMenuItemById = useCallback(
    (items: any[], id: string): MenuItem | undefined => {
      if (!Array.isArray(items)) return undefined;

      for (const item of items) {
        if (item.id === id) return item;
        if (item.sub) {
          const found = findMenuItemById(item.sub, id);
          if (found) return found;
        }
      }
      return undefined;
    },
    []
  );

  // 生成菜单 items
  const menuItems: any[] = useMemo(() => {
    if (!Array.isArray(menuList)) return [];

    const generateItems = (items: MenuItem[]): any[] => {
      return items.map((item) => {
        if (item.sub && item.sub.length === 1) {
          return {
            key: item.sub[0].id,
            label: item.sub[0].name,
            url: item.sub[0].uri,
            onClick: () => navigate(item.sub![0].uri || ''),
          };
        } else {
          return {
            key: item.id,
            label: item.name,
            url: item.uri,
            children:
              item.sub && item.sub.length > 1
                ? generateItems(item.sub)
                : undefined,
          };
        }
      });
    };

    return [
      {
        key: '9999',
        label: '首页',
        url: '/home',
        onClick: () => navigate('/home'),
      },
      ...generateItems(menuList),
    ];
  }, [menuList, navigate]);

  // 提取路由数据生成逻辑 - 优化：避免重复代码
  const generateRouterData = useCallback((items: any[]) => {
    return items
      .map((item) => {
        if (item.children) {
          return item.children.map((child: any) => ({
            key: child.key,
            url: child.url,
          }));
        } else {
          return {
            key: item.key,
            url: item.url,
          };
        }
      })
      .flat();
  }, []);

  // 查找父菜单键 - 优化：提取为独立函数
  const findParentKey = useCallback((path: string, items: any[]): string[] => {
    for (const item of items) {
      if (item.children) {
        const find = findParentKey(path, item.children);
        if (find.length) {
          return [item.key.toString()];
        }
      } else {
        if (item.url && path.includes(item.url)) {
          return [item.key.toString()];
        }
      }
    }
    return [];
  }, []);

  // 监听路由变化，自动展开对应父菜单 - 优化：使用提取的函数
  useEffect(() => {
    const parentKeys = findParentKey(location.pathname, menuItems);
    if (parentKeys.length) {
      setOpenKeys(parentKeys);
    }

    const routerData = generateRouterData(menuItems);
    const findSelectedKey = routerData.find(
      (item) => item.url === location.pathname
    );
    if (findSelectedKey) {
      setSelectedKeys([findSelectedKey.key.toString()]);
    }
  }, [location.pathname, menuItems, findParentKey, generateRouterData]);

  // 处理菜单点击 - 优化：使用提取的函数
  const handleMenuClick: MenuProps['onClick'] = useCallback(
    (e: any) => {
      const routerData = generateRouterData(menuItems);
      const findSelectedKey = routerData.find(
        (item) => item.key.toString() === e.key.toString()
      );
      if (findSelectedKey?.url) {
        navigate(findSelectedKey.url);
      }
    },
    [menuItems, generateRouterData, navigate]
  );

  // 处理菜单展开/折叠
  const handleOpenChange: MenuProps['onOpenChange'] = useCallback(
    (keys: string[]) => {
      // 只保持一个菜单组展开（手风琴效果）
      const latestOpenKey = keys[keys.length - 1];
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    },
    []
  );

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      breakpoint="lg"
      onBreakpoint={(broken) => broken && setCollapsed(true)}
      className="main-sider"
    >
      <div className="logo-vertical text-center h-[64px]">
        <div
          className={`transition-all duration-300 flex items-center justify-center h-full`}
        >
          {collapsed ? (
            <img
              src={clientInfo?.image as string}
              alt="Logo"
              className="w-10 h-10"
            />
          ) : (
            <div className="flex items-center space-x-2">
              <img
                src={clientInfo?.image as string}
                alt="Logo"
                className="w-10 h-10"
              />
              <span className="text-[14px] text-[#6180f5] font-bold whitespace-nowrap">
                {(clientInfo?.client_name as string) || '高尔夫管理系统'}
              </span>
            </div>
          )}
        </div>
      </div>

      {menuItems.length > 0 && (
        <div
          className="sider-scrollbar"
          style={{ height: 'calc(100vh - 64px)', overflow: 'auto' }}
        >
          <Menu
            mode="inline"
            className="menu-list"
            openKeys={openKeys}
            onOpenChange={handleOpenChange}
            onClick={handleMenuClick}
            items={menuItems}
            selectedKeys={selectedKeys}
          />
        </div>
      )}
    </Sider>
  );
};

export default React.memo(AppSider);
