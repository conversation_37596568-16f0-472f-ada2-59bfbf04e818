import { createRoot } from 'react-dom/client';
import './index.css';
import { App, ConfigProvider, type ThemeConfig } from 'antd';
import RouterView from './router/RouterView.tsx';
import 'antd/dist/reset.css';
import zhCN from 'antd/locale/zh_CN';
import { initLocale } from './utils/locale';

// 初始化国际化配置
initLocale();

const config: ThemeConfig = {
  token: {
    colorPrimary: '#6280F5',
    borderRadius: 2,
  },
  cssVar: true,
};

createRoot(document.getElementById('root')!).render(
  <ConfigProvider theme={config} locale={zhCN}>
    <App>
      <RouterView />
    </App>
  </ConfigProvider>
);
