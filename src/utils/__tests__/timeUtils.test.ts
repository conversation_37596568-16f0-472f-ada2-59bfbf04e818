import dayjs from 'dayjs';
import { parseTimeString, formatTime, validateTimeRange, createTimeRange } from '../timeUtils';

describe('timeUtils', () => {
  describe('parseTimeString', () => {
    it('should parse HH:mm:ss format', () => {
      const result = parseTimeString('12:08:23');
      expect(result).toBeDefined();
      expect(result?.isValid()).toBe(true);
      expect(result?.format('HH:mm:ss')).toBe('12:08:23');
    });

    it('should parse HH:mm format', () => {
      const result = parseTimeString('12:08');
      expect(result).toBeDefined();
      expect(result?.isValid()).toBe(true);
      expect(result?.format('HH:mm')).toBe('12:08');
    });

    it('should parse H:mm format', () => {
      const result = parseTimeString('9:08');
      expect(result).toBeDefined();
      expect(result?.isValid()).toBe(true);
      expect(result?.format('HH:mm')).toBe('09:08');
    });

    it('should return undefined for invalid time', () => {
      const result = parseTimeString('invalid');
      expect(result).toBeUndefined();
    });

    it('should return undefined for empty string', () => {
      const result = parseTimeString('');
      expect(result).toBeUndefined();
    });
  });

  describe('formatTime', () => {
    it('should format dayjs object', () => {
      const time = dayjs('12:08:23', 'HH:mm:ss');
      const result = formatTime(time, 'HH:mm');
      expect(result).toBe('12:08');
    });

    it('should format time string', () => {
      const result = formatTime('12:08:23', 'HH:mm');
      expect(result).toBe('12:08');
    });

    it('should return empty string for invalid input', () => {
      const result = formatTime('invalid');
      expect(result).toBe('');
    });
  });

  describe('validateTimeRange', () => {
    it('should validate valid time range', () => {
      const result = validateTimeRange('09:00:00', '17:00:00');
      expect(result).toBe(true);
    });

    it('should invalidate invalid time range', () => {
      const result = validateTimeRange('17:00:00', '09:00:00');
      expect(result).toBe(false);
    });

    it('should invalidate invalid time strings', () => {
      const result = validateTimeRange('invalid', '17:00:00');
      expect(result).toBe(false);
    });
  });

  describe('createTimeRange', () => {
    it('should create valid time range', () => {
      const result = createTimeRange('09:00:00', '17:00:00');
      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result?.[0].format('HH:mm')).toBe('09:00');
      expect(result?.[1].format('HH:mm')).toBe('17:00');
    });

    it('should return undefined for invalid times', () => {
      const result = createTimeRange('invalid', '17:00:00');
      expect(result).toBeUndefined();
    });

    it('should handle the specific case from the issue', () => {
      const result = createTimeRange('12:08:23', '12:08:23');
      expect(result).toBeDefined();
      expect(result?.[0].format('HH:mm')).toBe('12:08');
      expect(result?.[1].format('HH:mm')).toBe('12:08');
    });
  });
});
