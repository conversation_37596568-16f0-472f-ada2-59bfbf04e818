import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';

// 配置 dayjs 插件
dayjs.extend(customParseFormat);
dayjs.extend(relativeTime);
dayjs.extend(weekday);
dayjs.extend(localeData);

// 设置中文语言
dayjs.locale('zh-cn');

/**
 * 初始化国际化配置
 */
export const initLocale = () => {
  // 设置 dayjs 为中文
  dayjs.locale('zh-cn');

  // 可以在这里添加其他国际化配置
  console.log('国际化配置已初始化');
};

/**
 * 获取当前语言设置
 */
export const getCurrentLocale = () => {
  return dayjs.locale();
};

/**
 * 格式化日期为中文格式
 */
export const formatDateCN = (date: dayjs.ConfigType, format?: string) => {
  return dayjs(date).format(format || 'YYYY年MM月DD日');
};

/**
 * 格式化时间为中文格式
 */
export const formatTimeCN = (time: dayjs.ConfigType, format?: string) => {
  return dayjs(time).format(format || 'HH:mm');
};

/**
 * 获取相对时间（中文）
 */
export const getRelativeTimeCN = (date: dayjs.ConfigType) => {
  return dayjs(date).fromNow();
};

export default {
  initLocale,
  getCurrentLocale,
  formatDateCN,
  formatTimeCN,
  getRelativeTimeCN,
};
