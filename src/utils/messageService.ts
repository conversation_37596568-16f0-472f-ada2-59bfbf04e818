import { message } from 'antd';

/**
 * 消息服务类，用于在非React组件中使用message功能
 * 注意：这个服务仍然使用静态方法，但提供了一个统一的接口
 * 在未来可以考虑使用全局状态管理或者事件系统来完全替代
 */
class MessageService {
  private static instance: MessageService;

  private constructor() {}

  public static getInstance(): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService();
    }
    return MessageService.instance;
  }

  success(content: string, duration = 3) {
    message.success({ content, duration });
  }

  error(content: string, duration = 5) {
    message.error({ content, duration });
  }

  warning(content: string, duration = 3) {
    message.warning({ content, duration });
  }

  info(content: string, duration = 3) {
    message.info({ content, duration });
  }

  loading(content: string, duration = 0) {
    return message.loading({ content, duration });
  }
}

export const messageService = MessageService.getInstance();
export default messageService;
