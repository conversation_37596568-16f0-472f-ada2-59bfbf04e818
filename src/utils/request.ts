import axios from 'axios';

import { getToken } from './auth';
import { tansParams } from './golfTool';
import Cookies from 'js-cookie';
import { messageService } from './messageService';

const errorCode = {
  '401': '认证失败，无法访问系统资源',
  '403': '当前操作没有权限',
  '404': '访问资源不存在',
  default: '系统未知错误，请反馈给管理员',
};

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// 对应国际化资源文件后缀
axios.defaults.headers['Content-Language'] = 'zh_CN';
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 10000,
});

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 显示loading

    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    // URL前缀，默认是 admin
    const urlPrefix = (config.headers || {}).urlPrefix || 'v1/admin';

    // 支持自定义timeout
    if (config.timeout === undefined) {
      config.timeout = 20000; // 默认超时时间
    }

    // 处理 URL 前缀
    if (urlPrefix && !config.url?.startsWith(`/${urlPrefix}/`)) {
      config.url = `/${urlPrefix}/` + (config.url || '').replace(/^\//, '');
    }

    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    if (
      !isRepeatSubmit &&
      (config.method === 'post' || config.method === 'put')
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === 'object'
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      const sessionObj = JSON.parse(
        sessionStorage.getItem('sessionObj') || '{}'
      );
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ''
      ) {
        sessionStorage.setItem('sessionObj', JSON.stringify(requestObj));
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = '数据正在处理，请勿重复提交';
          console.warn(`[${s_url}]: ` + message);
          return Promise.reject(new Error(message));
        } else {
          sessionStorage.setItem('sessionObj', JSON.stringify(requestObj));
        }
      }
    }
    return config;
  },
  (error) => {
    // 隐藏loading
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 隐藏loading

    // 未设置状态码则默认成功状态
    const code = res.data.errno || 0;
    // 获取错误信息
    const msg =
      errorCode[code as keyof typeof errorCode] ||
      res.data.msg ||
      errorCode['default'];
    // 二进制数据则直接返回
    if (
      res.request.responseType === 'blob' ||
      res.request.responseType === 'arraybuffer'
    ) {
      return res.data;
    }
    if (code != 0) {
      if (msg) {
        if (code == 6 || code == 7 || code == 777) {
          messageService.error(
            res?.data?.data ? res.data.data.join(';') : msg,
            3
          );
          Cookies.remove('Admin-Token');
          window.location.reload();
        } else {
          messageService.error(
            res?.data?.data
              ? Array.from(new Set(res.data.data)).join(';')
              : msg,
            5
          );
        }
      }
      return Promise.reject('error');
    } else {
      return Promise.resolve(res.data);
    }
  },
  (error) => {
    // 隐藏loading
    let { message } = error;
    if (message == 'Network Error') {
      message = '后端接口连接异常';
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时';
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常';
    }
    messageService.error(message, 5);
    return Promise.reject(error);
  }
);

export default service;
