import dayjs, { Dayjs } from 'dayjs';

/**
 * 时间解析工具函数
 * 支持多种时间格式的解析，确保与TimePicker组件兼容
 */
export const parseTimeString = (timeStr: string): Dayjs | undefined => {
  if (!timeStr) return undefined;
  
  // 尝试不同的时间格式
  const formats = ['HH:mm:ss', 'HH:mm', 'H:mm:ss', 'H:mm'];
  
  for (const format of formats) {
    const parsed = dayjs(timeStr, format);
    if (parsed.isValid()) {
      return parsed;
    }
  }
  
  // 如果都解析失败，尝试直接解析
  const directParsed = dayjs(timeStr);
  return directParsed.isValid() ? directParsed : undefined;
};

/**
 * 格式化时间为指定格式
 */
export const formatTime = (time: Dayjs | string, format: string = 'HH:mm'): string => {
  if (!time) return '';
  
  if (typeof time === 'string') {
    const parsed = parseTimeString(time);
    return parsed ? parsed.format(format) : '';
  }
  
  return time.format(format);
};

/**
 * 验证时间范围是否有效
 */
export const validateTimeRange = (startTime: Dayjs | string, endTime: Dayjs | string): boolean => {
  const start = typeof startTime === 'string' ? parseTimeString(startTime) : startTime;
  const end = typeof endTime === 'string' ? parseTimeString(endTime) : endTime;
  
  if (!start || !end || !start.isValid() || !end.isValid()) {
    return false;
  }
  
  return start.isBefore(end);
};

/**
 * 创建时间范围数组，用于TimePicker.RangePicker
 */
export const createTimeRange = (startTime: string, endTime: string): [Dayjs, Dayjs] | undefined => {
  const start = parseTimeString(startTime);
  const end = parseTimeString(endTime);
  
  if (start && end && start.isValid() && end.isValid()) {
    return [start, end];
  }
  
  return undefined;
};
