import React, { Suspense } from 'react';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import Loading from './Loading';
import { generateRoutes } from './generator';
import AuthGuard from './AuthGuard';

const Layout = React.lazy(() => import('../layouts'));
const routes = generateRoutes();
const noLayoutRoutes = routes.filter((route) => !route.meta?.layout);
const layoutRoutes = routes.filter((route) => route.meta?.layout);

/**
 * 主路由视图组件
 */
const RouterView: React.FC = () => (
  <BrowserRouter>
    <Suspense fallback={<Loading />}>
      <Routes>
        {/* 需要权限的路由都放在 AuthGuard 里 */}
        <Route element={<AuthGuard />}>
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/home" replace />} />
            {layoutRoutes.map(({ path, component: Component }) => (
              <Route key={path} path={path} element={<Component />} />
            ))}
          </Route>
        </Route>
        {/* 不需要布局的路由 */}
        {noLayoutRoutes.map(({ path, component: Component }) => (
          <Route key={path} path={path} element={<Component />} />
        ))}

        {/* 兜底路由 */}
        <Route path="*" element={<Navigate to="/error/404" replace />} />
      </Routes>
    </Suspense>
  </BrowserRouter>
);

export default RouterView;
