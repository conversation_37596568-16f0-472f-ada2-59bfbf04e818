import { lazy } from 'react';
import { routeConfig } from './config';
import type { AppRouteRecordRaw } from './types';

/**
 * 路径转换工具函数
 */
const pathUtils = {
  filePathToRoutePath: (filePath: string): string => {
    let path = filePath
      .replace('../pages', '')
      .replace(/\.tsx?$/, '')
      .replace(/\/index$/i, '');
      
    if (path === '') path = '/';
    if (!path.startsWith('/')) path = '/' + path;
    
    return path;
  },
  
  generateRouteName: (path: string): string => {
    return path === '/' ? 'home' : path.slice(1).replace(/\//g, '-');
  },
  
  generateTitle: (name: string): string => {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/-/g, ' ');
  }
};

/**
 * 路由生成器
 */
export const generateRoutes = (): AppRouteRecordRaw[] => {
  // 获取所有页面组件
  const pageModules = import.meta.glob('../pages/**/*.tsx');
  
  return Object.entries(pageModules)
    .filter(([filePath]) => 
      !routeConfig.excludeDirs.some((dir: string) => filePath.includes(`/${dir}/`))
    )
    .map(([filePath, moduleLoader]) => {
      const path = pathUtils.filePathToRoutePath(filePath);
      const name = pathUtils.generateRouteName(path);
      
      return {
        path,
        name,
        component: lazy(moduleLoader as () => Promise<{ default: React.ComponentType<unknown> }>),
        meta: {
          ...routeConfig.defaultMeta,
          layout: !routeConfig.noLayoutRoutes.includes(path),
          title: pathUtils.generateTitle(name)
        }
      };
    });
};