.vben-loading-bg {
  position: fixed;
  inset: 0;
  z-index: 9999;
  background: #f5f6f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.vben-loading-center {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 48px;
}

.vben-cube {
  width: 64px;
  height: 64px;
  background: #0080ff;
  border-radius: 10px;
  position: absolute;
  left: 28px;
  top: 0;
  animation: cube-spin 1.2s linear infinite;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

@keyframes cube-spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.vben-shadow {
  position: absolute;
  left: 20px;
  top: 90px;
  width: 80px;
  height: 14px;
  background: #8ec3ff;
  opacity: 0.35;
  border-radius: 50%;
  filter: blur(1px);
  animation: shadow-scale 1.2s linear infinite;
}

@keyframes shadow-scale {
  0% { transform: scaleX(1);}
  50% { transform: scaleX(1.2) scaleY(0.8);}
  100% { transform: scaleX(1);}
}

.vben-title {
  font-size: 1.5rem;
  color: #222;
  letter-spacing: 2px;
  text-align: center;
  user-select: none;
}

/* React Transition Group 类名 */
.loading-enter {
  opacity: 0;
}

.loading-enter-active {
  opacity: 1;
  transition: opacity 200ms ease;
}

.loading-exit {
  opacity: 1;
}

.loading-exit-active {
  opacity: 0;
  transition: opacity 200ms ease;
} 