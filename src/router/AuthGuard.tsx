/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate, Outlet } from 'react-router-dom';
import { getToken } from '../utils/auth';
import useUserStore from '../store/useUserStore';
import useMemberRoleStore from '../store/useMemberRoleStore';
import { useMessage } from '../hooks/useMessage';
import Loading from './Loading';

const whiteList = ['/login', '/error/404'];

const AuthGuard: React.FC = () => {
  const message = useMessage();
  const location = useLocation();
  const navigate = useNavigate();
  const token = getToken();
  const userStore = useUserStore();
  const memberRoleStore = useMemberRoleStore();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (token) {
          if (location.pathname === '/login') {
            navigate('/', { replace: true });
            return;
          }
          if (!userStore.clientInfo?.id) {
            setLoading(true);
            try {
              await memberRoleStore.getOtherConfig();
              await memberRoleStore.getMemberRole();
              await userStore.getInfo();

              setLoading(false);
            } catch (error) {
              await userStore.logOut();
              message.error('获取用户信息失败，请重新登录');
              navigate('/login', { replace: true });
              setLoading(false);
            }
          }
        } else {
          if (whiteList.includes(location.pathname)) {
            return;
          }
          message.warning('请先登录');
          navigate(`/login?redirect=${encodeURIComponent(location.pathname)}`, {
            replace: true,
          });
        }
      } catch (error) {
        message.error('路由跳转异常');
      }
    };
    checkAuth();
    // eslint-disable-next-line
  }, [location.pathname]);

  return (
    <>
      {loading ? <Loading /> : null}
      <Outlet />
    </>
  );
};

export default AuthGuard;
