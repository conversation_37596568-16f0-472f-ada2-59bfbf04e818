import React from 'react';
import { createPortal } from 'react-dom';
import styles from './Loading.module.css';

const Loading: React.FC = () => {
  const loadingContent = (
    <div className={styles['vben-loading-bg']}>
      <div className={styles['vben-loading-center']}>
        <div className={styles['vben-cube']}></div>
        <div className={styles['vben-shadow']}></div>
      </div>
      <div className={styles['vben-title']}>加载中...</div>
    </div>
  );

  // 使用React Portal将loading渲染到body
  return createPortal(loadingContent, document.body);
};

export default Loading;
