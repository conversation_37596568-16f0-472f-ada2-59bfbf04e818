import React, { useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import { HomeOutlined } from '@ant-design/icons';
import styles from './404.module.css';
import error404 from '../../assets/images/404.png';

const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const message = '找不到网页！';

  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  return (
    <div className={styles['wscn-http404-container']}>
      <div className={styles['wscn-http404']}>
        <img className="w-1/2 mr-10" src={error404} alt="404页面" />
        <div className={styles.bullshit}>
          <div className={styles['bullshit__oops']}>404错误!</div>
          <div className={styles['bullshit__headline']}>{message}</div>
          <div className={styles['bullshit__info']}>
            对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。
          </div>
          <div
            className={`${styles['bullshit__actions']} flex justify-between`}
          >
            <Link to="/home" className={styles['bullshit__return-home']}>
              <HomeOutlined /> 返回首页
            </Link>
            <Button
              type="link"
              onClick={handleGoBack}
              className={styles['bullshit__go-back']}
            >
              返回上页
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
