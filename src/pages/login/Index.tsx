/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { Button, Form, Input, Checkbox, Card } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { UserOutlined, LockOutlined, TrophyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Cookies from 'js-cookie';
import './login.scss';
import useUserStore from '../../store/useUserStore';
import { useMessage } from '../../hooks/useMessage';

interface LoginForm {
  username: string;
  password: string;
  remember: boolean;
}

const Login: React.FC = () => {
  const message = useMessage();
  const login = useUserStore((state) => state.login);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [form, setForm] = useState<LoginForm>({
    username: Cookies.get('username') || '',
    password: Cookies.get('password') || '',
    remember: Cookies.get('remember') === 'true',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleCheckbox = (e: CheckboxChangeEvent) => {
    setForm({ ...form, remember: e.target.checked });
  };

  const handleSubmit = async (values: LoginForm) => {
    setLoading(true);
    try {
      await login({ ...values, clientId: 0 });
      // 记住我逻辑
      if (values.remember) {
        Cookies.set('username', values.username, {
          expires: 30,
          secure: true,
          sameSite: 'strict',
          path: '/',
        });
        Cookies.set('password', values.password, {
          expires: 30,
          secure: true,
          sameSite: 'strict',
          path: '/',
        });
        Cookies.set('remember', 'true', {
          expires: 30,
          secure: true,
          sameSite: 'strict',
          path: '/',
        });
      } else {
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('remember');
      }
      navigate('/home');
    } catch (err) {
      message.error('登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* 背景装饰 */}
      <div className="login-background">
        <div className="background-shapes">
          <div className="shape shape-1"></div>
          <div className="shape shape-2"></div>
          <div className="shape shape-3"></div>
        </div>
      </div>

      {/* 登录表单 */}
      <div className="login-content">
        <Card className="login-card">
          {/* Logo和标题 */}
          <div className="login-header text-center">
            <div className="logo-container !mb-6">
              <TrophyOutlined className="logo-icon" />
            </div>
            <h1 className="login-title !mb-4">高尔夫球场管理系统</h1>
          </div>

          {/* 登录表单 */}
          <Form
            name="login"
            initialValues={form}
            onFinish={handleSubmit}
            size="large"
            className="login-form"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名！' }]}
            >
              <Input
                prefix={<UserOutlined className="input-icon" />}
                placeholder="用户名"
                className="login-input"
                name="username"
                value={form.username}
                onChange={handleChange}
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码！' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="input-icon" />}
                placeholder="密码"
                className="login-input"
                name="password"
                value={form.password}
                onChange={handleChange}
              />
            </Form.Item>

            <Form.Item>
              <div className="flex justify-between items-center">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox checked={form.remember} onChange={handleCheckbox}>
                    记住我
                  </Checkbox>
                </Form.Item>
              </div>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="login-button w-full"
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          {/* 底部信息 */}
          <div className="login-footer text-center mt-6">
            <p className="text-xs text-gray-400 mt-2">
              © 2024 上海高球时代科技有限公司. All rights reserved.
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
