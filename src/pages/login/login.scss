.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    padding: 1rem;
  }
  // 背景装饰
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
  
  .background-shapes {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
    
    &.shape-1 {
      width: 8rem;
      height: 8rem;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 6rem;
      height: 6rem;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 4rem;
      height: 4rem;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-50px);
    }
  }

  // 登录内容区域
.login-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 32rem;
  }

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    box-shadow: 0 1.5rem 3rem rgba(0, 0, 0, 0.1);    
  }

  // Logo和标题
.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 4rem;
    height: 4rem;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
  }
  .logo-icon {
    font-size: 1.5rem;
    color: white;
  }

  .login-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    /* 移除margin-bottom，让Tailwind的mb-2生效 */
  }

  // 表单样式
.login-form {
    .ant-form-item {
      margin-bottom: 1.25rem;
    }
  }

// 表单样式
.login-form {
    .ant-form-item {
      margin-bottom: 1.25rem;
    }
  }
  
  .login-input {
    height: 3rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    font-size: 0.875rem;
    
    &:focus,
    &:hover {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }
  }
  
  .input-icon {
    color: #a0aec0;
    font-size: 1rem;
  }
  
  .forgot-password {
    color: #667eea;
    font-size: 0.875rem;
    text-decoration: none;
    
    &:hover {
      color: #5a67d8;
      text-decoration: underline;
    }
  }
  
  .login-button {
    height: 3rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-1px);
      box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.3);
    }
  }