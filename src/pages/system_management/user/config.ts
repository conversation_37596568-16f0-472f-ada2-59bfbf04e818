import type { TableColumnsType } from 'antd';

// 定义用户数据类型
export interface UserFormData {
  user_name: string;
  user_phone: string;
  is_phone_validate: '是' | '否';
  admin_name: string;
  password?: string;
  password_re?: string;
  role_id: number[];
}

export interface UserData {
  user_id: string;
  user_name: string;
  user_phone: string;
  role_id?: number[];
  role_name: string[];
  created_at: string;
  updated_at: string;
  is_phone_validate: string;
  admin_name: string;
  password: string;
  password_re: string;
}

// 基础列配置（不包含操作列）
export const columns: TableColumnsType<UserData> = [
  {
    title: '序号',
    key: 'index',
    width: 100,
    align: 'center',
    render: (_: UserData, __: UserData, index: number) => index + 1,
  },
  {
    title: '姓名',
    dataIndex: 'user_name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '手机号',
    dataIndex: 'user_phone',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '角色',
    dataIndex: 'role_name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    align: 'center',
    ellipsis: true,
    width: 180,
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    align: 'center',
    ellipsis: true,
    width: 180,
  },
];
