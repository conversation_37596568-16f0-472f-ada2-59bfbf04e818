/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useCallback } from 'react';
import type { UserData } from './config';
import {
  getRoleList,
  getUserList,
  setUserDelete,
  setUserPassword,
  setUserStore,
} from '../../../api/system';
import { useMessage } from '../../../hooks/useMessage';
import { Form } from 'antd';
import type { FormInstance } from 'antd/es/form';

export interface UserModalState {
  open: boolean;
  isEdit: boolean;
  form: FormInstance<any>;
  initialData?: UserData;
  show: (user?: UserData) => void;
  close: () => void;
  submit: () => Promise<void>;
}

export const useUserHook = (): {
  userList: UserData[];
  loading: boolean;
  fetchUserList: () => Promise<void>;
  fetchUserRole: () => Promise<void>;
  handleDelete: (record: UserData) => void;
  userModal: UserModalState;
  passwordModal: any;
  userRole: any[];
} => {
  const message = useMessage();

  // 列表相关
  const [userList, setUserList] = useState<UserData[]>([]);
  const [userRole, setUserRole] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 用户表单相关
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [userForm] = Form.useForm();
  const [editingUser, setEditingUser] = useState<UserData | null>(null);

  // 密码表单相关
  const [passwordModalOpen, setPasswordModalOpen] = useState(false);
  const [passwordForm] = Form.useForm();
  const [passwordUserId, setPasswordUserId] = useState<string>('');

  // 获取用户列表
  const fetchUserList = useCallback(async () => {
    try {
      setLoading(true);
      const res = await getUserList();
      setUserList(res.data || []);
    } catch (error) {
      message.error('获取用户列表失败: ' + error);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUserRole = useCallback(async () => {
    try {
      const res = await getRoleList();
      setUserRole(res.data || []);
    } catch (error) {
      message.error('获取角色列表失败: ' + error);
    }
  }, []);

  // 删除用户
  const handleDelete = useCallback(
    async (record: UserData) => {
      try {
        const res: any = await setUserDelete({ id: record.user_id });
        if (res.errno == 0) {
          message.success('删除用户成功');
          await fetchUserList();
        } else {
          message.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除用户失败:', error);
      }
    },
    [fetchUserList]
  );

  // 用户表单相关方法
  const userModal: UserModalState = {
    open: userModalOpen,
    initialData: editingUser || undefined,
    show: (user?: UserData) => {
      if (user) {
        setEditingUser(user);
        userForm.setFieldsValue({
          user_name: user.user_name,
          user_phone: user.user_phone,
          is_phone_validate: user.is_phone_validate,
          admin_name: user.admin_name,
          role_id: user.role_id,
        });
      } else {
        setEditingUser(null);
        userForm.resetFields();
      }
      setUserModalOpen(true);
    },
    close: () => {
      setUserModalOpen(false);
      userForm.resetFields();
      setEditingUser(null);
    },
    submit: async () => {
      try {
        const values = await userForm.validateFields();

        // 如果是编辑模式，移除密码相关字段
        if (editingUser) {
          delete values.password;
          delete values.password_re;
        }

        const params = editingUser
          ? { ...values, user_id: editingUser.user_id }
          : values;

        const res: any = await setUserStore(params);
        if (res.errno == 0) {
          message.success(editingUser ? '编辑成功' : '新增成功');
          userModal.close();
          await fetchUserList();
        } else {
          message.error(res.msg || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
      }
    },
    form: userForm,
    isEdit: !!editingUser,
  };

  // 密码表单相关方法
  const passwordModal = {
    open: passwordModalOpen,
    show: (userId: string) => {
      setPasswordUserId(userId);
      passwordForm.resetFields();
      setPasswordModalOpen(true);
    },
    close: () => {
      setPasswordModalOpen(false);
      passwordForm.resetFields();
      setPasswordUserId('');
    },
    submit: async () => {
      try {
        const values = await passwordForm.validateFields();
        const res: any = await setUserPassword({
          user_id: passwordUserId,
          password: values.new_password,
          password_confirmation: values.confirm_password,
        });

        if (res.errno == 0) {
          message.success('修改密码成功');
          passwordModal.close();
        } else {
          message.error(res.message || '修改密码失败');
        }
      } catch (error) {
        console.error('修改密码失败:', error);
      }
    },
    form: passwordForm,
  };

  return {
    // 列表相关
    userList,
    loading,
    fetchUserList,
    fetchUserRole,
    handleDelete,
    // 用户表单
    userModal,
    // 密码表单
    passwordModal,
    userRole,
  };
};
