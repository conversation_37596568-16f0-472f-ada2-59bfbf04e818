import React from 'react';
import { Form, Input } from 'antd';
import type { FormInstance } from 'antd/es/form';
import GolfModel from '../../../../components/golfModel';

interface PasswordFormData {
  new_password: string;
  confirm_password: string;
}

interface PasswordModalProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: () => void;
  form: FormInstance<PasswordFormData>;
}

const PasswordModal: React.FC<PasswordModalProps> = ({
  open,
  onCancel,
  onSubmit,
  form,
}) => {
  return (
    <GolfModel
      title="修改密码"
      open={open}
      onCancel={onCancel}
      onOk={onSubmit}
      width={500}
    >
      <div className="p-4">
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          layout="horizontal"
          autoComplete="off"
        >
          <Form.Item
            label="新密码"
            name="new_password"
            rules={[
              { required: true, message: '请输入新密码' },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d!@#$%^&*]+$/,
                message: '密码必须包含字母和数字',
              },
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>

          <Form.Item
            label="确认密码"
            name="confirm_password"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请再次输入密码" />
          </Form.Item>
        </Form>
      </div>
    </GolfModel>
  );
};

export default PasswordModal;
