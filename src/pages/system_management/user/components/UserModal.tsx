import React from 'react';
import { Form, Input, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';
import GolfModel from '../../../../components/golfModel';
import type { UserData, UserFormData } from '../config';

const { Option } = Select;

interface UserModalProps {
  isEdit: boolean;
  open: boolean;
  onCancel: () => void;
  onSubmit: () => void;
  initialData?: UserData;
  userRole: { role_id: number; role_name: string }[];
  form: FormInstance<UserFormData>;
}

const UserModal: React.FC<UserModalProps> = ({
  isEdit,
  open,
  onCancel,
  onSubmit,
  initialData,
  userRole,
  form,
}) => {
  const handleVisibleChange = (visible: boolean) => {
    if (visible) {
      // 延迟执行确保Form组件已挂载
      setTimeout(() => {
        if (initialData) {
          console.log(initialData);
          // 将UserData转换为UserFormData格式
          const formData: UserFormData = {
            user_name: initialData.user_name,
            user_phone: initialData.user_phone,
            is_phone_validate:
              initialData.is_phone_validate === '1' ? '是' : '否',
            admin_name: initialData.admin_name,
            role_id: initialData?.role_id || [],
          };
          form.setFieldsValue(formData);
        } else {
          // 完全重置表单数据
          form.resetFields();
        }
      }, 0);
    } else {
      // 模态框关闭时重置表单
      form.resetFields();
    }
  };
  return (
    <GolfModel
      title={isEdit ? '编辑用户' : '新增用户'}
      open={open}
      onCancel={onCancel}
      onOk={onSubmit}
      width={500}
      onVisibleChange={handleVisibleChange}
    >
      <div className="p-4">
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          layout="horizontal"
          autoComplete="off"
        >
          <Form.Item
            label="姓名"
            name="user_name"
            rules={[
              { required: true, message: '请输入姓名' },
              { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' },
            ]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            label="手机号"
            name="user_phone"
            rules={[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
            ]}
          >
            <Input placeholder="请输入手机号" maxLength={11} />
          </Form.Item>

          <Form.Item label="手机号验证登录" name="is_phone_validate">
            <Select placeholder="请选择">
              <Option value="是">是</Option>
              <Option value="否">否</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="账号"
            name="admin_name"
            rules={[{ required: true, message: '请输入账号' }]}
          >
            <Input placeholder="请输入账号" />
          </Form.Item>

          <Form.Item
            hidden={isEdit}
            label="密码"
            name="password"
            rules={!isEdit ? [{ required: true, message: '请输入密码' }] : []}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            hidden={isEdit}
            label="确认密码"
            name="password_re"
            rules={
              !isEdit
                ? [
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          new Error('两次输入的密码不一致')
                        );
                      },
                    }),
                  ]
                : []
            }
          >
            <Input.Password placeholder="请再次输入密码" />
          </Form.Item>

          <Form.Item
            label="角色"
            name="role_id"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择角色"
              options={userRole.map((item) => ({
                label: item.role_name,
                value: item.role_id,
              }))}
            />
          </Form.Item>
        </Form>
      </div>
    </GolfModel>
  );
};

export default UserModal;
