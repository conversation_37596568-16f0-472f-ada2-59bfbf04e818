import { Button, Popconfirm, Table } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { columns, type UserData } from './config';
import { useUserHook, type UserModalState } from './useUserHook';
import UserModal from './components/UserModal.tsx';
import PasswordModal from './components/PasswordModal.tsx';

const User: React.FC = () => {
  const {
    userList,
    loading,
    fetchUserList,
    handleDelete,
    userModal,
    passwordModal,
    fetchUserRole,
    userRole,
  } = useUserHook();

  useEffect(() => {
    fetchUserList();
    fetchUserRole();
  }, [fetchUserList, fetchUserRole]);

  // 扩展列配置，添加操作列
  const tableColumns = useMemo(
    () => [
      ...columns,
      {
        title: '操作',
        key: 'action',
        align: 'center' as const,
        width: 250,
        render: (_: UserData, record: UserData) => (
          <div className="flex items-center justify-center gap-2">
            <Button type="link" onClick={() => userModal.show(record)}>
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => passwordModal.show(record.user_id)}
            >
              修改密码
            </Button>
            <Popconfirm
              title="确认删除"
              description={`确定要删除用户 "${record.user_name}" 吗？`}
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ],
    [handleDelete, passwordModal, userModal]
  );

  return (
    <div className="h-full w-full overflow-hidden flex flex-col">
      <div className="tool-box flex items-center">
        <Button type="primary" onClick={() => userModal.show()}>
          新增用户
        </Button>
      </div>

      <div className="table-container flex-1 p-4 bg-white mt-4">
        <Table
          rowKey="user_id"
          columns={tableColumns}
          dataSource={userList}
          loading={loading}
          pagination={false}
          scroll={{ y: 'calc(100vh - 230px)' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />
      </div>

      {/* 新增/编辑用户弹窗 */}
      <UserModal
        isEdit={userModal.isEdit}
        open={userModal.open}
        onCancel={userModal.close}
        onSubmit={userModal.submit}
        initialData={(userModal as UserModalState).initialData}
        userRole={userRole}
        form={userModal.form}
      />

      {/* 修改密码弹窗 */}
      <PasswordModal
        open={passwordModal.open}
        onCancel={passwordModal.close}
        onSubmit={passwordModal.submit}
        form={passwordModal.form}
      />
    </div>
  );
};

export default User;
