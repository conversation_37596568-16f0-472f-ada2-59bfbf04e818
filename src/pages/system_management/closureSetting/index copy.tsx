import React, { useState, useEffect } from 'react';
import { 
  Input, 
  Button, 
  Table, 
  Form, 
  Modal, 
  Switch, 
  Radio, 
  Select, 
  Checkbox, 
  DatePicker, 
  TimePicker, 
  Pagination, 
  message,
  Space
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import { SearchOutlined } from '@ant-design/icons';
import useUserStore from '../../../store/useUserStore';
import {
  getClosureList,
  setClosureStatus,
  setClosureDelete,
  saveClosureSchemeAPI
} from '../../../api/system';
import './ClosureSetting.scss';

// 类型定义
interface Partition {
  id: number;
  part_name: string;
}

interface ClosureItem {
  id: string;
  closure_name: string;
  date_type_name: string;
  start_time: string;
  end_time: string;
  partition_names: string;
  effective_date_start: string;
  effective_date_end: string;
  is_open: number;
  date_type: number;
  week: number | null;
  month_start: string;
  month_end: string;
  date_start: string;
  date_end: string;
  partition_ids: string;
}

interface FormClosureData {
  id?: string;
  closure_name: string;
  date_type: number | null;
  date: [string, string];
  month: [string, string];
  time: [string, string];
  week: number | null;
  partition_ids: number[];
  effective_date_start: string;
  effective_date_end: string;
}

// 选项数据
const date_typeOptions = [
  { label: '每天', value: 1 },
  { label: '每周', value: 3 },
  { label: '每月', value: 2 },
  { label: '特定日期', value: 4 }
];

const weeklyOptions = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 0 }
];

const ClosureSetting: React.FC = () => {
  // 状态管理
  const [searchValue, setSearchValue] = useState('');
  const [venueList, setVenueList] = useState<ClosureItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [form] = Form.useForm<FormClosureData>();
  const [golfStyles, setGolfStyles] = useState<Partition[]>([]);
  const [dateType, setDateType] = useState<number | null>(null);
  const userStore = useUserStore();
  
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  
  // 表格列定义
  const columns: ColumnsType<ClosureItem> = [
    {
      title: '名称',
      dataIndex: 'closure_name',
      key: 'closure_name',
      align: 'center'
    },
    {
      title: '日期类型',
      dataIndex: 'date_type_name',
      key: 'date_type_name',
      align: 'center'
    },
    {
      title: '时间',
      key: 'time',
      align: 'center',
      render: (_, record) => `${record.start_time} - ${record.end_time}`
    },
    {
      title: '分区',
      dataIndex: 'partition_names',
      key: 'partition_names',
      align: 'center',
      render: (text) => text || '-'
    },
    {
      title: '生效日期',
      dataIndex: 'effective_date_start',
      key: 'effective_date_start',
      align: 'center'
    },
    {
      title: '截止日期',
      dataIndex: 'effective_date_end',
      key: 'effective_date_end',
      align: 'center'
    },
    {
      title: '状态',
      key: 'is_open',
      align: 'center',
      render: (_, record) => (
        <Switch
          checked={record.is_open === 1}
          onChange={(checked) => handleSwitch(record, checked)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handelEdit(record)}>
            编辑
          </Button>
          <Button type="link" danger onClick={() => handelDelete(record)}>
            删除
          </Button>
        </div>
      )
    }
  ];

  // 表单验证规则
  const validateDateType = () => ({
    validator(_: any, value: number) {
      if (!value && value !== 0) {
        return Promise.reject(new Error('请选择封场日期类型'));
      }
      return Promise.resolve();
    }
  });

  // 获取表格数据
  const getTableData = async () => {
    setLoading(true);
    try {
      const res = await getClosureList({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchValue
      });
      
      setVenueList(res.data.list || []);
      setPagination({
        ...pagination,
        total: res.data.page?.total_count || 0
      });
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchData = async () => {
    try {

      const partitions = userStore?.partitions?.[0] as any;
      if (partitions?.part_info) {
        setGolfStyles(partitions.part_info);
      }
      await getTableData();
    } catch (error) {
      message.error('初始化数据失败');
    }
  };

  // 初始化数据
  useEffect(() => {

    
    fetchData();
  }, []);

  // 当分页或搜索条件变化时重新获取数据
  useEffect(() => {
    getTableData();
  }, [pagination.current, pagination.pageSize, searchValue]);

  // 状态切换处理
  const handleSwitch = async (row: ClosureItem, checked: boolean) => {
    try {
      await setClosureStatus(row.id, {
        is_open: checked ? 1 : 0
      });
      message.success('状态变更成功');
      
      // 更新本地状态
      setVenueList(prev => prev.map(item => 
        item.id === row.id ? { ...item, is_open: checked ? 1 : 0 } : item
      ));
    } catch (error) {
      message.error('状态变更失败');
    }
  };

  // 编辑处理
  const handelEdit = (row: ClosureItem) => {
    form.setFieldsValue({
      id: row.id,
      closure_name: row.closure_name,
      date_type: row.date_type,
      week: row.week,
      month: [row.month_start, row.month_end] as [string, string],
      date: [row.date_start, row.date_end] as [string, string],
      time: [row.start_time, row.end_time] as [string, string],
      partition_ids: row.partition_ids.split('|').map(id => parseInt(id)),
      effective_date_start: row.effective_date_start,
      effective_date_end: row.effective_date_end
    });
    
    // 设置日期类型状态以正确显示日期选择器
    setDateType(row.date_type);
    setDialogVisible(true);
  };

  // 删除处理
  const handelDelete = (row: ClosureItem) => {
    Modal.confirm({
      title: '删除确认',
      content: '确定删除该封场吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await setClosureDelete({ id: row.id });
          message.success('删除成功');
          getTableData();
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  // 分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination({
      current: page,
      pageSize: pageSize || pagination.pageSize,
      total: pagination.total
    });
  };

  // 日期类型变化
  const changeDataType = (e: any) => {
    const dateType = e.target.value;
    form.setFieldsValue({
      date: undefined,
      month: undefined,
      week: undefined
    });
    setDateType(dateType);
  };

  // 禁用日期（未来180天内）
  const disabledDates = (current: Dayjs) => {
    const today = dayjs().startOf('day');
    const maxDate = today.add(180, 'day');
    return current < today || current > maxDate;
  };

  // 禁用今日之前的日期
  const disabledDate = (current: Dayjs) => {
    return current < dayjs().startOf('day');
  };

  // 新增封场
  const handleAddClosure = () => {
    form.resetFields();
    setDateType(null);
    setDialogVisible(true);
  };

  // 提交表单
  const handleSubmitClosure = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理日期数据
      const formatDate = (date: any) => {
        if (Array.isArray(date) && date[0] && date[1]) {
          return [date[0].format('YYYY-MM-DD'), date[1].format('YYYY-MM-DD')];
        }
        return ['', ''];
      };
      
      const formatTime = (time: any) => {
        if (Array.isArray(time) && time[0] && time[1]) {
          return [time[0].format('HH:mm'), time[1].format('HH:mm')];
        }
        return ['', ''];
      };
      
      const params = {
        id: values.id,
        closure_name: values.closure_name,
        date_type: values.date_type,
        month_start: values.month?.[0]?.format?.('DD') || '',
        month_end: values.month?.[1]?.format?.('DD') || '',
        date_start: values.date?.[0]?.format?.('YYYY-MM-DD') || '',
        date_end: values.date?.[1]?.format?.('YYYY-MM-DD') || '',
        start_time: values.time?.[0]?.format?.('HH:mm') || '',
        end_time: values.time?.[1]?.format?.('HH:mm') || '',
        partition_ids: values.partition_ids.join('|'),
        effective_date_start: values.effective_date_start?.format?.('YYYY-MM-DD') || '',
        effective_date_end: values.effective_date_end?.format?.('YYYY-MM-DD') || '',
        week: values.week
      };
      
      const res = await saveClosureSchemeAPI(params);
      if (res.errno === 0) {
        message.success(values.id ? '编辑成功' : '新增成功');
        setDialogVisible(false);
        getTableData();
      } else {
        message.error(res.msg || '操作失败');
      }
    } catch (error) {
      console.error('表单验证失败', error);
    }
  };

  // 关闭对话框
  const closeCarDialog = () => {
    form.resetFields();
    setDialogVisible(false);
  };

  return (
    <div className="closure-setting">
      <div className="table-box-search">
        <Input
          placeholder="请输入封场名称搜索"
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            setPagination({ ...pagination, current: 1 });
          }}
          style={{ width: 260 }}
          suffix={<SearchOutlined />}
          allowClear
        />
        <Button 
          type="primary" 
          onClick={handleAddClosure}
          className="add-button"
        >
          新增封场
        </Button>
      </div>
      
      <div className="table-box">
        <Table
          columns={columns}
          dataSource={venueList}
          loading={loading}
          pagination={false}
          scroll={{ y: 'calc(100vh - 280px)' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          rowKey="id"
        />
      </div>
      
      <div className="pagination-container">
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger
          onShowSizeChange={(current, size) => 
            setPagination({ ...pagination, pageSize: size, current: 1 })
          }
          showTotal={total => `共 ${total} 条`}
        />
      </div>
      
      <Modal
        title={form.getFieldValue('id') ? '编辑封场' : '新增封场'}
        open={dialogVisible}
        onCancel={closeCarDialog}
        onOk={handleSubmitClosure}
        width={500}
        centered
        className="closure-modal"
        footer={[
          <Button key="back" onClick={closeCarDialog}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSubmitClosure}>
            确定
          </Button>
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          className="closure-form"
        >
          <Form.Item
            label="封场方案名称"
            name="closure_name"
            rules={[
              { required: true, message: '请输入封场方案名称' },
              { min: 2, max: 50, message: '名称长度在 2 到 50 个字符' }
            ]}
          >
            <Input placeholder="请输入封场方案名称" />
          </Form.Item>
          
          <Form.Item
            label="封场日期类型"
            name="date_type"
            rules={[validateDateType]}
          >
            <Radio.Group onChange={changeDataType}>
              <Space direction="vertical">
                {date_typeOptions.map(item => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
            
            <div className="date-select">
              {dateType === 3 && (
                <Form.Item name="week" className="date-item">
                  <Select placeholder="请选择星期" style={{ width: '100%' }}>
                    {weeklyOptions.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
              
              {dateType === 2 && (
                <Form.Item name="month" className="date-item">
                  <DatePicker.RangePicker 
                    placeholder={['开始日', '结束日']}
                    format="DD"
                    picker="date"
                    disabledDate={disabledDates}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}
              
              {dateType === 4 && (
                <Form.Item name="date" className="date-item">
                  <DatePicker.RangePicker 
                    placeholder={['开始日期', '结束日期']}
                    format="YYYY-MM-DD"
                    disabledDate={disabledDates}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}
            </div>
          </Form.Item>
          
          <Form.Item
            label="封场时间范围"
            name="time"
            rules={[{ required: true, message: '请选择封场时间范围' }]}
          >
            <TimePicker.RangePicker 
              format="HH:mm" 
              style={{ width: '100%' }} 
            />
          </Form.Item>
          
          <Form.Item
            label="封场分区"
            name="partition_ids"
            rules={[{ required: true, message: '请选择封场分区' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div className="partition-grid">
                {golfStyles.map(item => (
                  <Checkbox key={item.id} value={item.id} className="partition-checkbox">
                    {item.part_name}
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>
          
          <Form.Item
            label="生效日期"
            name="effective_date_start"
            rules={[{ required: true, message: '请选择生效日期' }]}
          >
            <DatePicker 
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }} 
            />
          </Form.Item>
          
          <Form.Item
            label="截止日期"
            name="effective_date_end"
            rules={[{ required: true, message: '请选择截止日期' }]}
          >
            <DatePicker 
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }} 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ClosureSetting;