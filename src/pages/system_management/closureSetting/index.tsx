import {
  But<PERSON>,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Radio,
  Select,
  Space,
  Switch,
  Table,
  TimePicker,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import {
  getClosureList,
  setClosureDelete,
  setClosureStatus,
} from '../../../api/system';
import { useMessage } from '../../../hooks/useMessage';

// 类型定义
interface Partition {
  id: number;
  part_name: string;
}

interface ClosureItem {
  id: string;
  closure_name: string;
  date_type_name: string;
  start_time: string;
  end_time: string;
  partition_names: string;
  effective_date_start: string;
  effective_date_end: string;
  is_open: number;
  date_type: number;
  week: number | null;
  month_start: string;
  month_end: string;
  date_start: string;
  date_end: string;
  partition_ids: string;
}

interface FormClosureData {
  id?: string;
  closure_name: string;
  date_type: number | null;
  date: [string, string];
  month: [string, string];
  time: [string, string];
  week: number | null;
  partition_ids: number[];
  effective_date_start: string;
  effective_date_end: string;
}

// 选项数据
const date_typeOptions = [
  { label: '每天', value: 1 },
  { label: '每周', value: 3 },
  { label: '每月', value: 2 },
  { label: '特定日期', value: 4 }
];

const weeklyOptions = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 0 }
];

const ClosureSetting: React.FC = () => {
  // 表格列定义
  const columns: ColumnsType<ClosureItem> = [
    {
      title: '名称',
      dataIndex: 'closure_name',
      key: 'closure_name',
      align: 'center',
    },
    {
      title: '日期类型',
      dataIndex: 'date_type_name',
      key: 'date_type_name',
      align: 'center',
    },
    {
      title: '时间',
      key: 'time',
      align: 'center',
      render: (_, record) => `${record.start_time} - ${record.end_time}`,
    },
    {
      title: '分区',
      dataIndex: 'partition_names',
      key: 'partition_names',
      align: 'center',
      render: (text) => text || '-',
    },
    {
      title: '生效日期',
      dataIndex: 'effective_date_start',
      key: 'effective_date_start',
      align: 'center',
    },
    {
      title: '截止日期',
      dataIndex: 'effective_date_end',
      key: 'effective_date_end',
      align: 'center',
    },
    {
      title: '状态',
      key: 'is_open',
      align: 'center',
      render: (_, record) => (
        <Switch
          checked={record.is_open === 1}
          onChange={(checked) => handleSwitch(record, checked)}
        />
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handelEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定删除该封场吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const [searchValue, setSearchValue] = useState('');
  const [venueList, setVenueList] = useState<ClosureItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<FormClosureData>();
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dateType, setDateType] = useState<number | null>(null);
  const [golfStyles, setGolfStyles] = useState<Partition[]>([])

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const message = useMessage();

  // 状态切换处理
  const handleSwitch = async (row: ClosureItem, checked: boolean) => {
    try {
      await setClosureStatus(row.id, {
        is_open: checked ? 1 : 0,
      });
      message.success('状态变更成功');

      // 更新本地状态
      setVenueList((prev) =>
        prev.map((item) =>
          item.id === row.id ? { ...item, is_open: checked ? 1 : 0 } : item
        )
      );
    } catch (error) {
      message.error('状态变更失败' + error);
    }
  };

  // 获取表格数据
  const getTableData = async () => {
    setLoading(true);
    try {
      const res = await getClosureList({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchValue,
      });

      setVenueList(res.data.list || []);
      setPagination({
        ...pagination,
        total: res.data.page?.total_count || 0,
      });
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };


  const handleDelete = async (row: ClosureItem) => {
    try {
      await setClosureDelete({ id: row.id });
      message.success('删除成功');
      getTableData();
    } catch (error) {
      message.error('删除失败' + error);
    }
  };

  // 分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination({
      current: page,
      pageSize: pageSize || pagination.pageSize,
      total: pagination.total,
    });
  };

  // 关闭对话框
  const closeCarDialog = () => {
      form.resetFields();
      setDialogVisible(false);
  };

    // 提交表单
    const handleSubmitClosure = async () => {
      console.log(form.getFieldsValue());
    };

    // 禁用日期（未来180天内）
    const disabledDates = (current: Dayjs) => {
      const today = dayjs().startOf('day');
      const maxDate = today.add(180, 'day');
      return current < today || current > maxDate;
    };
  
    // 禁用今日之前的日期
    const disabledDate = (current: Dayjs) => {
      return current < dayjs().startOf('day');
    };

      // 表单验证规则
  const validateDateType = () => ({
    validator(_: any, value: number) {
      if (!value && value !== 0) {
        return Promise.reject(new Error('请选择封场日期类型'));
      }
      return Promise.resolve();
    }
  });

    // 日期类型变化
    const changeDataType = (e: any) => {
      const dateType = e.target.value;
      form.setFieldsValue({
        date: undefined,
        month: undefined,
        week: undefined
      });
      setDateType(dateType);
    };

      // 新增封场
  const handleAddClosure = () => {
    form.resetFields();
    setDateType(null);
    setDialogVisible(true);
  };

    // 编辑处理
    const handelEdit = (row: ClosureItem) => {

          // 设置日期类型状态以正确显示日期选择器
      setDateType(row.date_type);
      setDialogVisible(true);
    };

  // 当分页或搜索条件变化时重新获取数据
  useEffect(() => {
    getTableData();
  }, [pagination.current, pagination.pageSize, searchValue]);


  return (
    <div className="h-full w-full overflow-hidden flex flex-col bg-white p-4">
      <div className="table-box-search flex items-center justify-between">
        <Input
          placeholder="请输入封场名称搜索"
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            setPagination({ ...pagination, current: 1 });
          }}
          style={{ width: 260 }}
          suffix={<SearchOutlined />}
          allowClear
        />
        <Button
          type="primary"
          onClick={handleAddClosure}
          className="add-button"
        >
          新增封场
        </Button>
      </div>
      <div className="table-box mt-4 overflow-hidden h-[calc(100vh-110px)]">
        <Table
          columns={columns}
          dataSource={venueList}
          loading={loading}
          pagination={false}
          scroll={{ y: 'calc(100vh - 280px)' }}
          style={{ height: '100%' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          rowKey="id"
        />
      </div>
      <div className="pagination-container flex justify-end">
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger
          onShowSizeChange={(current, size) => 
            setPagination({ ...pagination, pageSize: size, current: 1 })
          }
          showTotal={total => `共 ${total} 条`}
        />
      </div>

      <Modal
        title={form.getFieldValue('id') ? '编辑封场' : '新增封场'}
        open={dialogVisible}
        onCancel={closeCarDialog}
        onOk={handleSubmitClosure}
        width={500}
        centered
        className="closure-modal"
        footer={[
          <Button key="back" onClick={closeCarDialog}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSubmitClosure}>
            确定
          </Button>
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          className="closure-form"
        >
          <Form.Item
            label="封场方案名称"
            name="closure_name"
            rules={[
              { required: true, message: '请输入封场方案名称' },
              { min: 2, max: 50, message: '名称长度在 2 到 50 个字符' }
            ]}
          >
            <Input placeholder="请输入封场方案名称" />
          </Form.Item>
          
          <Form.Item
            label="封场日期类型"
            name="date_type"
            rules={[validateDateType]}
          >
            <Radio.Group onChange={changeDataType}>
              <Space direction="vertical">
                {date_typeOptions.map(item => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
            
            <div className="date-select">
              {dateType === 3 && (
                <Form.Item name="week" className="date-item">
                  <Select placeholder="请选择星期" style={{ width: '100%' }}>
                    {weeklyOptions.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
              
              {dateType === 2 && (
                <Form.Item name="month" className="date-item">
                  <DatePicker.RangePicker 
                    placeholder={['开始日', '结束日']}
                    format="DD"
                    picker="date"
                    disabledDate={disabledDates}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}
              
              {dateType === 4 && (
                <Form.Item name="date" className="date-item">
                  <DatePicker.RangePicker 
                    placeholder={['开始日期', '结束日期']}
                    format="YYYY-MM-DD"
                    disabledDate={disabledDates}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}
            </div>
          </Form.Item>
          
          <Form.Item
            label="封场时间范围"
            name="time"
            rules={[{ required: true, message: '请选择封场时间范围' }]}
          >
            <TimePicker.RangePicker 
              format="HH:mm" 
              style={{ width: '100%' }} 
            />
          </Form.Item>
          
          <Form.Item
            label="封场分区"
            name="partition_ids"
            rules={[{ required: true, message: '请选择封场分区' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div className="partition-grid">
                {golfStyles.map(item => (
                  <Checkbox key={item.id} value={item.id} className="partition-checkbox">
                    {item.part_name}
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>
          
          <Form.Item
            label="生效日期"
            name="effective_date_start"
            rules={[{ required: true, message: '请选择生效日期' }]}
          >
            <DatePicker 
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }} 
            />
          </Form.Item>
          
          <Form.Item
            label="截止日期"
            name="effective_date_end"
            rules={[{ required: true, message: '请选择截止日期' }]}
          >
            <DatePicker 
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }} 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ClosureSetting;
