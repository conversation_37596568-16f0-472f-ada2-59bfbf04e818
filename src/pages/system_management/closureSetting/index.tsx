import {
  Button,
  Form,
  Input,
  Pagination,
  Popconfirm,
  Space,
  Switch,
  Table,
} from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import {
  getClosureList,
  setClosureDelete,
  setClosureStatus,
  saveClosureSchemeAPI,
} from '../../../api/system';
import { useMessage } from '../../../hooks/useMessage';
import useUserStore from '../../../store/useUserStore';
import ClosureModal from './components/closureModal';

// 类型定义
interface Partition {
  id: number;
  part_name: string;
}

interface ClosureItem {
  id: string;
  closure_name: string;
  date_type_name: string;
  start_time: string;
  end_time: string;
  partition_names: string;
  effective_date_start: string;
  effective_date_end: string;
  is_open: number;
  date_type: number;
  week: number | null;
  month_start: string;
  month_end: string;
  date_start: string;
  date_end: string;
  partition_ids: string;
}

interface FormClosureData {
  id?: string;
  closure_name: string;
  date_type: number | null;
  date: string[];
  month: string[];
  time: [dayjs.Dayjs, dayjs.Dayjs];
  week: number | null;
  partition_ids: number[];
  effective_date_start: dayjs.Dayjs;
  effective_date_end?: dayjs.Dayjs;
}



const ClosureSetting: React.FC = () => {
  // 表格列定义
  const columns: ColumnsType<ClosureItem> = [
    {
      title: '名称',
      dataIndex: 'closure_name',
      key: 'closure_name',
      align: 'center',
    },
    {
      title: '日期类型',
      dataIndex: 'date_type_name',
      key: 'date_type_name',
      align: 'center',
    },
    {
      title: '时间',
      key: 'time',
      align: 'center',
      render: (_, record) => `${record.start_time} - ${record.end_time}`,
    },
    {
      title: '分区',
      dataIndex: 'partition_names',
      key: 'partition_names',
      align: 'center',
      render: (text) => text || '-',
    },
    {
      title: '生效日期',
      dataIndex: 'effective_date_start',
      key: 'effective_date_start',
      align: 'center',
    },
    {
      title: '截止日期',
      dataIndex: 'effective_date_end',
      key: 'effective_date_end',
      align: 'center',
    },
    {
      title: '状态',
      key: 'is_open',
      align: 'center',
      render: (_, record) => (
        <Switch
          checked={record.is_open === 1}
          onChange={(checked) => handleSwitch(record, checked)}
        />
      ),
    },
    {
      title: '操作',
      key: 'operation',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定删除该封场吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const message = useMessage();
  const userStore = useUserStore();

  // 状态管理
  const [searchValue, setSearchValue] = useState('');
  const [venueList, setVenueList] = useState<ClosureItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<FormClosureData>();
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dateType, setDateType] = useState<number | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<ClosureItem | null>(null);

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 获取分区数据
  const partitions = useMemo(() => {
    const partitionData = userStore?.partitions?.[0] as { part_info?: Partition[] };
    return partitionData?.part_info || [];
  }, [userStore.partitions]);

  // 状态切换处理
  const handleSwitch = useCallback(async (row: ClosureItem, checked: boolean) => {
    try {
      await setClosureStatus(row.id, {
        is_open: checked ? 1 : 0,
      });
      message.success('状态变更成功');

      // 更新本地状态
      setVenueList((prev) =>
        prev.map((item) =>
          item.id === row.id ? { ...item, is_open: checked ? 1 : 0 } : item
        )
      );
    } catch (error) {
      message.error('状态变更失败');
      console.error('状态变更失败:', error);
    }
  }, [message]);

  // 获取表格数据
  const getTableData = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getClosureList({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchValue,
      });

      setVenueList(res.data.list || []);
      setPagination(prev => ({
        ...prev,
        total: res.data.page?.total_count || 0,
      }));
    } catch (error) {
      message.error('获取数据失败');
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, [pagination.current, pagination.pageSize, searchValue, message]);


  // 删除处理
  const handleDelete = useCallback(async (row: ClosureItem) => {
    try {
      await setClosureDelete({ id: row.id });
      message.success('删除成功');
      await getTableData();
    } catch (error) {
      message.error('删除失败');
      console.error('删除失败:', error);
    }
  }, [message, getTableData]);

  // 分页变化
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  }, []);

  // 关闭对话框
  const closeCarDialog = useCallback(() => {
    form.resetFields();
    setDialogVisible(false);
    setIsEditing(false);
    setEditingRecord(null);
    setDateType(null);
  }, [form]);

  // 提交表单
  const handleSubmitClosure = useCallback(async () => {
    try {
      const values = await form.validateFields();
      console.log(values);
      // 处理时间范围
      const timeRange = values.time as [Dayjs, Dayjs];
      const formattedData: any = {
        ...values,
        start_time: timeRange[0].format('HH:mm'),
        end_time: timeRange[1].format('HH:mm'),
        effective_date_start: (values.effective_date_start as Dayjs).format('YYYY-MM-DD'),
        date_type: dateType,
      };

      // 移除不需要的字段
      delete formattedData.time;

      await saveClosureSchemeAPI(formattedData);
      message.success(isEditing ? '编辑成功' : '新增成功');
      closeCarDialog();
      await getTableData();
    } catch (error: any) {
      if (error?.errorFields) {
        // 表单验证错误，不显示错误消息
        return;
      }
      message.error(isEditing ? '编辑失败' : '新增失败');
      console.error('提交失败:', error);
    }
  }, [form, dateType, isEditing, message, closeCarDialog, getTableData]);



  // 日期类型变化
  const changeDataType = useCallback((e: import('antd/es/radio').RadioChangeEvent) => {
    const dateType = e.target.value;
    form.setFieldsValue({
      date: undefined,
      month: undefined,
      week: undefined
    });
    setDateType(dateType);
  }, [form]);

  // 新增封场
  const handleAddClosure = useCallback(() => {
    form.resetFields();
    setDateType(null);
    setIsEditing(false);
    setEditingRecord(null);
    setDialogVisible(true);
  }, [form]);

  // 编辑处理
  const handleEdit = useCallback((row: ClosureItem) => {
    setIsEditing(true);
    setEditingRecord(row);
    setDateType(row.date_type);
    setDialogVisible(true);
  }, []);

  // 初始化数据获取
  useEffect(() => {
    getTableData();
  }, [getTableData]);

  // 当搜索条件变化时重新获取数据
  useEffect(() => {
    if (pagination.current !== 1) {
      setPagination(prev => ({ ...prev, current: 1 }));
    } else {
      getTableData();
    }
  }, [searchValue]);


  return (
    <div className="h-full w-full overflow-hidden flex flex-col bg-white p-4">
      <div className="table-box-search flex items-center justify-between">
        <Input
          placeholder="请输入封场名称搜索"
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            setPagination({ ...pagination, current: 1 });
          }}
          style={{ width: 260 }}
          suffix={<SearchOutlined />}
          allowClear
        />
        <Button
          type="primary"
          onClick={handleAddClosure}
          className="add-button"
        >
          新增封场
        </Button>
      </div>
      <div className="table-box mt-4 overflow-hidden h-[calc(100vh-110px)]">
        <Table
          columns={columns}
          dataSource={venueList}
          loading={loading}
          pagination={false}
          scroll={{ y: 'calc(100vh - 280px)' }}
          style={{ height: '100%' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          rowKey="id"
        />
      </div>
      <div className="pagination-container flex justify-end">
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          onChange={handlePageChange}
          showSizeChanger
          onShowSizeChange={(_, size) =>
            setPagination(prev => ({ ...prev, pageSize: size, current: 1 }))
          }
          showTotal={total => `共 ${total} 条`}
        />
      </div>

      <ClosureModal
        open={dialogVisible}
        isEditing={isEditing}
        editingRecord={editingRecord}
        partitions={partitions}
        dateType={dateType}
        form={form}
        onCancel={closeCarDialog}
        onSubmit={handleSubmitClosure}
        onDateTypeChange={changeDataType}
      />
    </div>
  );
};

export default ClosureSetting;
