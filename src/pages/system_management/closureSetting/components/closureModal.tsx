import React, { useCallback, useEffect } from 'react';
import {
  Form,
  Input,
  Radio,
  Select,
  Checkbox,
  DatePicker,
  TimePicker,
} from 'antd';
import type { FormInstance } from 'antd/es/form';
import type { RadioChangeEvent } from 'antd/es/radio';
import dayjs, { Dayjs } from 'dayjs';
import GolfModel from '../../../../components/golfModel';

// 类型定义
interface Partition {
  id: number;
  part_name: string;
}

interface ClosureItem {
  id: string;
  closure_name: string;
  date_type_name: string;
  start_time: string;
  end_time: string;
  partition_names: string;
  effective_date_start: string;
  effective_date_end: string;
  is_open: number;
  date_type: number;
  week: number | null;
  month_start: string;
  month_end: string;
  date_start: string;
  date_end: string;
  partition_ids: string;
}

interface FormClosureData {
  id?: string;
  closure_name: string;
  date_type: number | null;
  date: [Dayjs, Dayjs];
  month: [Dayjs, Dayjs];
  time: [Dayjs, Dayjs];
  week: number | null;
  partition_ids: number[];
  effective_date_start: Dayjs;
  effective_date_end?: Dayjs;
}

interface ClosureModalProps {
  open: boolean;
  isEditing: boolean;
  editingRecord: ClosureItem | null;
  partitions: Partition[];
  dateType: number | null;
  form: FormInstance<FormClosureData>;
  onCancel: () => void;
  onSubmit: () => void;
  onDateTypeChange: (e: RadioChangeEvent) => void;
}

const ClosureModal: React.FC<ClosureModalProps> = ({
  open,
  isEditing,
  editingRecord,
  partitions,
  dateType,
  form,
  onCancel,
  onSubmit,
  onDateTypeChange,
}) => {
  // 禁用今日之前的日期
  const disabledDate = useCallback((current: Dayjs) => {
    return current < dayjs().startOf('day');
  }, []);

  // 表单验证规则
  const validateDateType = useCallback(
    () => ({
      validator(_: unknown, value: number) {
        if (!value && value !== 0) {
          return Promise.reject(new Error('请选择封场日期类型'));
        }
        return Promise.resolve();
      },
    }),
    []
  );

  // 当模态框打开时设置表单数据
  useEffect(() => {
    if (open && isEditing && editingRecord) {
      console.log('编辑数据回填:', editingRecord);

      const startTime = dayjs(editingRecord.start_time, 'HH:mm');
      const endTime = dayjs(editingRecord.end_time, 'HH:mm');

      console.log('解析后的时间:', { startTime, endTime });

      const formData: Partial<FormClosureData> = {
        week: editingRecord.week,
        id: editingRecord.id,
        closure_name: editingRecord.closure_name,
        date_type: editingRecord.date_type,
        time: startTime && endTime ? [startTime, endTime] : undefined,
        date:
          editingRecord.date_start && editingRecord.date_end
            ? [dayjs(editingRecord.date_start), dayjs(editingRecord.date_end)]
            : undefined,
        effective_date_start: editingRecord.effective_date_start
          ? dayjs(editingRecord.effective_date_start)
          : undefined,
        effective_date_end: editingRecord.effective_date_end
          ? dayjs(editingRecord.effective_date_end)
          : undefined,
        partition_ids: editingRecord.partition_ids
          ? editingRecord.partition_ids.split('|').map(Number)
          : [],
        month: [editingRecord.month_start, editingRecord.month_end].map(
          (date) => dayjs(date).month(0).date(Number(date))
        ) as unknown as [Dayjs, Dayjs],
      };

      console.log('设置表单数据:', formData);
      form.setFieldsValue(formData);
    } else if (open && !isEditing) {
      form.resetFields();
    }
  }, [open, isEditing, editingRecord, form]);

  return (
    <GolfModel
      title={isEditing ? '编辑封场' : '新增封场'}
      open={open}
      onCancel={onCancel}
      onOk={onSubmit}
      width={500}
    >
      <div className="p-4">
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          layout="horizontal"
          autoComplete="off"
        >
          <Form.Item
            label="封场名称"
            name="closure_name"
            rules={[
              { required: true, message: '请输入封场名称' },
              { max: 50, message: '封场名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入封场名称" />
          </Form.Item>

          <Form.Item
            label="封场日期类型"
            name="date_type"
            rules={[validateDateType]}
          >
            <Radio.Group onChange={onDateTypeChange}>
              <Radio value={1}>每天</Radio>
              <Radio value={3}>每周</Radio>
              <Radio value={2}>每月</Radio>
              <Radio value={4}>特定日期</Radio>
            </Radio.Group>
          </Form.Item>

          {dateType === 4 && (
            <Form.Item
              label="选择日期"
              name="date"
              rules={[{ required: true, message: '请选择日期' }]}
            >
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Form.Item>
          )}

          {dateType === 2 && (
            <Form.Item
              label="选择日期"
              name="month"
              rules={[{ required: true, message: '请选择月份' }]}
            >
              <DatePicker.RangePicker
                format="DD"
                key="month"
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Form.Item>
          )}

          {dateType === 3 && (
            <Form.Item
              label="选择星期"
              name="week"
              rules={[{ required: true, message: '请选择星期' }]}
            >
              <Select placeholder="请选择星期" style={{ width: '100%' }}>
                <Select.Option value={1}>星期一</Select.Option>
                <Select.Option value={2}>星期二</Select.Option>
                <Select.Option value={3}>星期三</Select.Option>
                <Select.Option value={4}>星期四</Select.Option>
                <Select.Option value={5}>星期五</Select.Option>
                <Select.Option value={6}>星期六</Select.Option>
                <Select.Option value={0}>星期日</Select.Option>
              </Select>
            </Form.Item>
          )}

          <Form.Item
            label="封场时间范围"
            name="time"
            rules={[{ required: true, message: '请选择封场时间范围' }]}
          >
            <TimePicker.RangePicker
              format="HH:mm"
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Form.Item
            label="封场分区"
            name="partition_ids"
            rules={[{ required: true, message: '请选择封场分区' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div className="partition-grid">
                {partitions.map((item: Partition) => (
                  <Checkbox
                    key={item.id}
                    value={item.id}
                    className="partition-checkbox"
                  >
                    {item.part_name}
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            label="生效日期"
            name="effective_date_start"
            rules={[{ required: true, message: '请选择生效日期' }]}
          >
            <DatePicker
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }}
              placeholder="请选择生效日期"
            />
          </Form.Item>
          <Form.Item
            label="截止日期"
            name="effective_date_end"
            rules={[{ required: true, message: '请选择生效日期' }]}
          >
            <DatePicker
              format="YYYY-MM-DD"
              disabledDate={disabledDate}
              style={{ width: '100%' }}
              placeholder="请选择截止日期"
            />
          </Form.Item>
        </Form>
      </div>
    </GolfModel>
  );
};

export default ClosureModal;
