.business-point-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tool-box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.business-point-content {
  flex: 1;
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: 0; /* 添加最小高度确保flex布局正确 */
}

.search-box {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap; /* 允许搜索框在小屏幕换行 */
}

.table-box {
  height: calc(100% - 64px);
  overflow: auto; /* 将hidden改为auto允许滚动 */
}

/* 自定义Ant Design组件样式 */
:global {
  .ant-table-wrapper {
    height: 100%;
    overflow: auto;
  }

  .ant-modal-body {
    padding: 20px;
  }

  .ant-popconfirm-inner-content {
    padding: 12px;
  }

  .ant-popover-inner {
    min-width: 200px !important;
  }
}