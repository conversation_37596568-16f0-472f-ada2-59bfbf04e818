/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import {
  Button,
  Table,
  Form,
  Input,
  Tree,
  Tooltip,
  Space,
  Popconfirm,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { TreeProps, DataNode } from 'antd/es/tree';
import type { ColumnsType } from 'antd/es/table';

import GolfModel from '../../../components/golfModel';
import { getRoleList, setAddRole, deleteRole } from '../../../api/system';
import useUserStore from '../../../store/useUserStore';
import { useMessage } from '../../../hooks/useMessage';

// 类型定义
interface MenuItem {
  id: string;
  name: string;
  sub?: MenuItem[];
  state?: number;
}

interface Role {
  role_id: number;
  role_name: string;
  menu_name: string[];
  created_at: string;
  updated_at: string;
}

interface RoleFormValues {
  role_name: string;
  menu_id: string[];
}

interface RoleModalState {
  open: boolean;
  isEditing: boolean;
  editingRoleId: number | null;
}

// API响应类型定义
interface ApiResponse<T = any> {
  errno: number;
  errmsg?: string;
  data?: T;
}

// 自定义hooks
const useRoleData = (message: ReturnType<typeof useMessage>) => {
  const [dataSource, setDataSource] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchRoleList = useCallback(async () => {
    setLoading(true);
    try {
      const res = (await getRoleList()) as unknown as ApiResponse<Role[]>;
      setDataSource(res.data || []);
    } catch (error) {
      message.error('获取角色列表失败');
      console.error('Failed to fetch role list:', error);
    } finally {
      setLoading(false);
    }
  }, [message]);

  return { dataSource, loading, fetchRoleList };
};

const useMenuTree = (menuList: MenuItem[]) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [menuDataList, setMenuDataList] = useState<DataNode[]>([]);

  const processMenuData = useCallback(() => {
    if (!menuList?.length) return;

    // 设置展开的keys
    const expandedKeys = menuList.map((item) => item.id);
    setExpandedKeys(expandedKeys);

    // 转换菜单数据格式
    const menuData = menuList.map((item: MenuItem) => ({
      ...item,
      key: item.id,
      title: item.name,
      children:
        item.sub?.map((subItem) => ({
          ...subItem,
          key: subItem.id,
          title: subItem.name,
        })) || [],
    }));

    setMenuDataList(menuData);
  }, [menuList]);

  useEffect(() => {
    processMenuData();
  }, [processMenuData]);

  return { expandedKeys, menuDataList };
};

const Role: React.FC = () => {
  const message = useMessage();
  const { menuList, clientInfo } = useUserStore();
  const { dataSource, loading, fetchRoleList } = useRoleData(message);
  const { expandedKeys, menuDataList } = useMenuTree(menuList);

  const [modalState, setModalState] = useState<RoleModalState>({
    open: false,
    isEditing: false,
    editingRoleId: null,
  });
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [form] = Form.useForm<RoleFormValues>();
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  useEffect(() => {
    setTimeout(() => {
      if (modalState.open && scrollContainerRef.current) {
        scrollContainerRef.current.scrollTop = 0;
      }
    }, 100);
  }, [modalState.open]);

  // 初始化数据
  useEffect(() => {
    fetchRoleList();
  }, [fetchRoleList]);

  // 模态框操作
  const openModal = useCallback(
    (role?: Role) => {
      if (role) {
        // 编辑模式
        setModalState({
          open: true,
          isEditing: true,
          editingRoleId: role.role_id,
        });

        // 设置表单值
        form.setFieldsValue({ role_name: role.role_name });

        // 处理权限选中状态
        setTimeout(() => {
          const menuNames = role.menu_name || [];
          const allMenuItems = menuList.flatMap((item: MenuItem) => [
            item,
            ...(item.sub || []),
          ]);

          const checkedIds = allMenuItems
            .filter((item: MenuItem) => menuNames.includes(item.name))
            .map((item: MenuItem) => item.id);

          setCheckedKeys(checkedIds);
          form.setFieldValue('menu_id', checkedIds);
        }, 0);
      } else {
        // 新增模式
        setModalState({
          open: true,
          isEditing: false,
          editingRoleId: null,
        });
        form.resetFields();
        setCheckedKeys([]);
      }
    },
    [form, menuList]
  );

  const closeModal = useCallback(() => {
    setModalState({
      open: false,
      isEditing: false,
      editingRoleId: null,
    });
    form.resetFields();
    setCheckedKeys([]);
  }, [form]);

  // 树形选择处理
  const handleTreeCheck: TreeProps['onCheck'] = useCallback(
    (keys: any, info: any) => {
      if (!keys) return;

      const checkedKeys = Array.isArray(keys) ? keys : keys.checked || [];
      const halfCheckedKeys = info.halfCheckedKeys || [];
      const allKeys = [...checkedKeys, ...halfCheckedKeys];

      setCheckedKeys(checkedKeys as string[]);
      form.setFieldValue('menu_id', allKeys);
    },
    [form]
  );

  // 表单提交
  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const payload = {
        name: values.role_name,
        menu_id: values.menu_id,
        role_id: modalState.editingRoleId,
        client_id: clientInfo.id,
      };

      const res = (await setAddRole(payload)) as unknown as ApiResponse;

      if (res.errno == 0) {
        message.success(modalState.isEditing ? '编辑成功' : '新增成功');
        fetchRoleList();
        closeModal();
      } else {
        message.error(res.errmsg || '操作失败');
      }
    } catch (error) {
      console.error('Submit failed:', error);
      message.error('表单验证失败');
    }
  }, [form, modalState, clientInfo.id, fetchRoleList, closeModal]);

  // 删除角色
  const handleDelete = useCallback(
    async (roleId: number) => {
      try {
        const res = (await deleteRole({
          id: roleId,
        })) as unknown as ApiResponse;
        if (res.errno == 0) {
          message.success('删除成功');
          fetchRoleList();
        } else {
          message.error(res.errmsg || '删除失败');
        }
      } catch (error) {
        console.error('Delete failed:', error);
        message.error('删除失败');
      }
    },
    [fetchRoleList]
  );

  // 表格列定义
  const columns: ColumnsType<Role> = useMemo(
    () => [
      {
        title: '序号',
        key: 'index',
        width: 80,
        align: 'center',
        render: (_, __, index) => index + 1,
      },
      {
        title: '角色',
        dataIndex: 'role_name',
        key: 'role_name',
        align: 'center',
        width: 150,
      },
      {
        title: '权限',
        dataIndex: 'menu_name',
        key: 'menu_name',
        align: 'center',
        render: (menuNames: string[]) => {
          const text = Array.isArray(menuNames) ? menuNames.join(', ') : '';
          return (
            <Tooltip
              title={text}
              placement="top"
              color="#6280F5"
              styles={{ body: { maxWidth: '600px', width: 'auto' } }}
            >
              <div
                style={{
                  maxWidth: '100%',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {text}
              </div>
            </Tooltip>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        align: 'center',
        width: 150,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        align: 'center',
        width: 150,
      },
      {
        title: '操作',
        key: 'operation',
        width: 150,
        align: 'center',
        render: (_, record) => (
          <Space size="small">
            <Button type="link" onClick={() => openModal(record)}>
              编辑
            </Button>
            <Popconfirm
              title="确认删除"
              description="确定要删除这个角色吗？"
              onConfirm={() => handleDelete(record.role_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ],
    [openModal, handleDelete]
  );

  return (
    <div className="h-full w-full overflow-hidden flex flex-col">
      <div className="tool-box flex items-center">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          新增角色
        </Button>
      </div>

      <div className="table-container flex-1 p-4 bg-white mt-4">
        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
          rowKey="role_id"
          scroll={{ y: 'calc(100vh - 230px)' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        />
      </div>

      <GolfModel
        title={modalState.isEditing ? '编辑角色' : '新增角色'}
        open={modalState.open}
        onCancel={closeModal}
        onOk={handleSubmit}
        width={700}
      >
        <div className="p-4 h-[40rem] overflow-y-auto" ref={scrollContainerRef}>
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleSubmit}
            autoComplete="off"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Form.Item
              label="角色名称"
              name="role_name"
              rules={[
                { required: true, message: '请输入角色名称' },
                { max: 50, message: '角色名称不能超过50个字符' },
              ]}
            >
              <Input placeholder="请输入角色名称" />
            </Form.Item>

            <Form.Item
              label="权限"
              name="menu_id"
              rules={[{ required: true, message: '请选择权限' }]}
            >
              <Tree
                checkable
                expandedKeys={expandedKeys}
                checkedKeys={checkedKeys}
                treeData={menuDataList}
                onCheck={handleTreeCheck}
                fieldNames={{
                  children: 'children',
                  title: 'title',
                  key: 'key',
                }}
                style={{
                  marginLeft: 16,
                  marginTop: 8,
                }}
              />
            </Form.Item>
          </Form>
        </div>
      </GolfModel>
    </div>
  );
};

export default Role;
