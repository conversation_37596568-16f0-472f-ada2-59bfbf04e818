import React, { useState, useEffect } from 'react';
import { Form, Select, Input, Button } from 'antd';

import useMemberRoleStore from '../../../store/useMemberRoleStore';
import { setOtherConfig } from '../../../api/system';
import { useMessage } from '../../../hooks/useMessage';
import _ from 'lodash';

interface RoleOption {
  id: string | number;
  name: string;
}

interface RoleConfigItem {
  name: string;
}

interface CommonPhraseItem {
  label: string;
}

interface ConfigForm {
  role?: string | number;
  teamRole?: string | number;
  vipRole?: string | number;
  searchRole?: string | number;
  roleConfig: RoleConfigItem[];
  CommonPhrases: CommonPhraseItem[];
}

interface RoleSearchOption {
  id: number;
  label: string;
}

const OtherConfig: React.FC = () => {
  const message = useMessage();
  const memberRoleStore = useMemberRoleStore();
  const [form] = Form.useForm<ConfigForm>();
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);
  const [loading, setLoading] = useState(false);

  const roleSearch: RoleSearchOption[] = [
    { id: 1, label: '模糊匹配' },
    { id: 2, label: '精确匹配' },
  ];

  // 初始化表单数据
  useEffect(() => {
    setRoleOptions(memberRoleStore.memberRole);

    if (memberRoleStore.sysConfig) {
      const clonedConfig = _.cloneDeep(memberRoleStore.sysConfig);
      form.setFieldsValue({
        ...clonedConfig,
        roleConfig: clonedConfig.roleConfig || [{ name: '' }],
        CommonPhrases: clonedConfig.CommonPhrases || [{ label: '' }],
      });
    }
  }, [memberRoleStore.memberRole, memberRoleStore.sysConfig, form]);

  const handleSave = async (values: ConfigForm) => {
    setLoading(true);
    try {
      // 过滤空值的常用语
      const processedConfig = {
        ...values,
        CommonPhrases: values.CommonPhrases.filter(
          (item) => item.label.trim() !== ''
        ),
      };

      const res = (await setOtherConfig({
        is_multi_packet: 0,
        sys_config: JSON.stringify(processedConfig),
      })) as unknown as { errno: number; errmsg?: string };

      if (res.errno == 0) {
        message.success('保存成功');
        memberRoleStore.getOtherConfig();
      } else {
        message.error('保存失败');
      }
    } catch {
      message.error('请求出错');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="other-config bg-white p-6 w-full h-full">
      <Form
        form={form}
        layout="horizontal"
        initialValues={{
          roleConfig: [{ name: '' }],
          CommonPhrases: [{ label: '' }],
        }}
        onFinish={handleSave}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        className="max-w-[800px]"
      >
        {/* 套餐身份配置 */}
        <Form.Item label="套餐身份配置" name="role">
          <Select
            placeholder="套餐身份配置"
            className="w-[300px]"
            allowClear
            options={roleOptions.map((item) => ({
              value: item.id,
              label: item.name,
            }))}
          />
        </Form.Item>

        {/* 散团套餐身份配置 */}
        <Form.Item label="散团套餐身份配置" name="teamRole">
          <Select
            placeholder="套餐身份配置"
            className="w-[300px]"
            allowClear
            options={roleOptions.map((item) => ({
              value: item.id,
              label: item.name,
            }))}
          />
        </Form.Item>

        {/* 会员标识身份配置 */}
        <Form.Item label="会员标识身份配置" name="vipRole">
          <Select
            placeholder="套餐身份配置"
            className="w-[300px]"
            allowClear
            options={roleOptions.map((item) => ({
              value: item.id,
              label: item.name,
            }))}
          />
        </Form.Item>

        {/* 搜索方式配置 */}
        <Form.Item label="搜索方式配置" name="searchRole">
          <Select
            placeholder="搜索方式配置"
            className="w-[300px]"
            options={roleSearch.map((item) => ({
              value: item.id,
              label: item.label,
            }))}
          />
        </Form.Item>

        {/* 身份展示顺序配置 - 动态表单项 */}
        <Form.Item label="身份展示顺序配置">
          <Form.List name="roleConfig">
            {(fields, { add, remove }) => (
              <div className="space-y-2">
                {fields.map((field, index) => (
                  <div key={field.key} className="flex items-center gap-2 mb-4">
                    <Form.Item
                      {...field}
                      name={[field.name, 'name']}
                      className="!mb-0 flex-1"
                    >
                      <Select
                        placeholder="请选择"
                        options={roleOptions.map((item) => ({
                          value: item.name,
                          label: item.name,
                        }))}
                      />
                    </Form.Item>

                    <Button
                      type="primary"
                      onClick={() => add()}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      添加
                    </Button>

                    {index > 0 && (
                      <Button
                        danger
                        onClick={() => remove(field.name)}
                        className="bg-red-500 hover:bg-red-600 text-white"
                      >
                        删除
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Form.List>
        </Form.Item>

        {/* 消息常用语配置 - 动态表单项 */}
        <Form.Item label="消息常用语配置">
          <Form.List name="CommonPhrases">
            {(fields, { add, remove }) => (
              <div className="space-y-2">
                {fields.map((field, index) => (
                  <div key={field.key} className="flex items-center gap-2 mb-4">
                    <Form.Item
                      {...field}
                      name={[field.name, 'label']}
                      className="!mb-0 flex-1"
                    >
                      <Input
                        placeholder="请输入常用语内容"
                        className="w-[400px]"
                      />
                    </Form.Item>

                    <Button
                      type="primary"
                      onClick={() => add({ label: '' })}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      添加
                    </Button>

                    {index > 0 && (
                      <Button
                        danger
                        onClick={() => remove(field.name)}
                        className="bg-red-500 hover:bg-red-600 text-white"
                      >
                        删除
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Form.List>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className="bg-blue-500 hover:bg-blue-600 px-8"
          >
            保存
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default OtherConfig;
