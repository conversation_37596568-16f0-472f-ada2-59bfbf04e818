.rule-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 12px;
  .header-title {
    font-size: 16px;
    font-weight: 500;
    color: #535c69;
    border-left: 3px solid #6180f5;
    padding-left: 10px;
  }
}
.rule-list-content {
  height: calc(100% - 62px);
  overflow-y: auto;
  padding: 0 12px;
  flex-shrink: 0;
  .rule-list-content-item {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #535c69;
    cursor: pointer;
    padding: 0 12px;
    &:hover {
      background-color: #6180f5;
      color: #fff;
    }
  }
  .active {
    background-color: #6180f5;
    color: #fff;
  }
}
.rule-example {
  .module {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-right: 36px;
    margin-top: 12px;
    min-width: 80px;

    .data {
      font-size: 24px;
      color: #333;
      font-weight: 500;
      margin-bottom: 12px;
    }
  }
}
.coding-rules-container {
  height: 100%;
  overflow: hidden;
  display: flex;
}
.rule-list {
  background: white;
  margin-right: 12px;
  height: 100%;
  width: 300px;
}
.rule-content {
  background: white;
  height: 100%;
  width: 100%;
  padding: 12px;
}
.rule-config-content {
  margin-top: 12px;
}
.rule-form {
  margin-top: 36px;
  width: 500px;
}