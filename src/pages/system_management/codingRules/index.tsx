/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Form, App } from 'antd';
import { getNumberRule, updateNumberRule } from '../../../api/system';
import RuleExample from './components/RuleExample';
import RuleForm from './components/RuleForm';
import LoadingState from './components/LoadingState';
import { formatNumber, getYearText, formatPrefix } from './config';
import type { RuleFormValues } from './types';
import './index.scss';

export default function CodingRules() {
  const { message } = App.useApp();
  const [form] = Form.useForm<RuleFormValues>();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 表单值状态
  const [formValues, setFormValues] = useState<Partial<RuleFormValues>>({});

  // 计算年份文本
  const yearText = useMemo(() => {
    const yearType = formValues.yearType || 1;
    return getYearText(yearType);
  }, [formValues.yearType]);

  // 计算流水号文本
  const serialNumberText = useMemo(() => {
    const length = Number(formValues.serialNumberLength || 0);
    return formatNumber(length);
  }, [formValues.serialNumberLength]);

  // 获取前缀文本
  const prefixText = useMemo(() => {
    const prefix = formValues.prefix;
    return formatPrefix(prefix);
  }, [formValues.prefix]);

  // 处理表单值变化
  const handleFormValuesChange = useCallback(
    (_changedValues: Partial<RuleFormValues>, allValues: RuleFormValues) => {
      setFormValues(allValues);
    },
    []
  );

  // 初始化表单数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setInitialLoading(true);
        setError(null);
        const response = await getNumberRule();
        const initialValues = {
          prefix: response.data.number_prefix || '',
          yearType: response.data.year_type || 1,
          monthType: response.data.month_type || 1,
          dayType: response.data.date_type || 1,
          serialNumberLength: response.data.serial_number_digit || '',
        };
        // 使用 setTimeout 确保 Form 组件已经渲染
        setTimeout(() => {
          form.setFieldsValue(initialValues);
        }, 0);
        setFormValues(initialValues);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : '获取编码规则失败';
        setError(errorMessage);
        message.error(errorMessage);
      } finally {
        setInitialLoading(false);
      }
    };
    fetchData();
  }, [form]);

  // 处理表单提交
  const handleSubmit = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const values = await form.validateFields();
      const response = await updateNumberRule({
        number_prefix: values.prefix,
        year_type: values.yearType,
        month_type: values.monthType,
        date_type: values.dayType,
        serial_number_digit: values.serialNumberLength,
      });
      if (response.errno === 0) {
        message.success('更新成功');
      } else {
        throw new Error(response.msg || '更新失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '提交失败';
      setError(errorMessage);
      if (err instanceof Error && err.message !== '提交失败') {
        message.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  }, [form]);

  return (
    <LoadingState loading={initialLoading} error={error}>
      <div className="coding-rules-container h-full overflow-hidden flex">
        <div className="rule-list bg-white mr-[12px] h-full w-[300px]">
          <div className="rule-header p-[12px]">
            <div className="header-title">编码规则</div>
          </div>
          <div className="rule-list-content mt-[12px]">
            <div className="rule-list-content-item active">商品编号</div>
          </div>
        </div>

        <div className="rule-content bg-white h-full w-full p-[12px]">
          <div className="rule-header">
            <div className="header-title">商品编号</div>
          </div>

          <div className="rule-config-content mt-[12px]">
            <RuleExample
              prefix={prefixText}
              yearText={yearText}
              serialNumberText={serialNumberText}
            />

            <RuleForm
              form={form}
              loading={loading}
              onSubmit={handleSubmit}
              onValuesChange={handleFormValuesChange}
            />
          </div>
        </div>
      </div>
    </LoadingState>
  );
}
