import dayjs from 'dayjs';

// 编码规则相关常量
export const RULE_CONSTANTS = {
  // 年份类型
  YEAR_TYPES: {
    TWO_DIGIT: 1,
    FOUR_DIGIT: 2,
  },

  // 月份和日期类型
  DATE_TYPES: {
    TWO_DIGIT: 1,
  },

  // 流水号位数范围
  SERIAL_NUMBER: {
    MIN: 1,
    MAX: 5,
  },

  // 默认值
  DEFAULTS: {
    YEAR_TYPE: 1,
    MONTH_TYPE: 1,
    DAY_TYPE: 1,
    SERIAL_NUMBER_LENGTH: '3',
  },
} as const;

// 表单验证规则
export const VALIDATION_RULES = {
  prefix: [{ required: true, message: '请输入编号前缀' }],
  yearType: [{ required: true, message: '请选择年份类型' }],
  monthType: [{ required: true, message: '请选择月份类型' }],
  dayType: [{ required: true, message: '请选择日期类型' }],
  serialNumberLength: [
    { required: true, message: '请输入流水号位数' },
    {
      pattern: /^[1-5]$/,
      message: '请输入1-5之间的整数',
    },
  ],
};

// 错误消息
export const ERROR_MESSAGES = {
  FETCH_FAILED: '获取编码规则失败',
  UPDATE_FAILED: '更新失败',
  SUBMIT_FAILED: '提交失败',
  NETWORK_ERROR: '网络连接异常',
  TIMEOUT_ERROR: '请求超时',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  UPDATE_SUCCESS: '更新成功',
} as const;

/**
 * 格式化数字为指定长度的字符串
 * @param n 数字
 * @returns 格式化后的字符串
 */
export const formatNumber = (n: number): string => {
  if (
    n > RULE_CONSTANTS.SERIAL_NUMBER.MAX ||
    n < RULE_CONSTANTS.SERIAL_NUMBER.MIN ||
    !Number.isInteger(n)
  ) {
    return '-';
  }
  return '0'.repeat(n - 1) + '1';
};

/**
 * 根据年份类型获取年份文本
 * @param yearType 年份类型
 * @returns 年份文本
 */
export const getYearText = (yearType: number): string => {
  const currentYear = dayjs().year();
  return yearType === RULE_CONSTANTS.YEAR_TYPES.FOUR_DIGIT
    ? currentYear.toString()
    : currentYear.toString().slice(-2);
};

/**
 * 获取月份文本（固定为2位数）
 * @returns 月份文本
 */
export const getMonthText = (): string => {
  return dayjs().format('MM');
};

/**
 * 获取日期文本（固定为2位数）
 * @returns 日期文本
 */
export const getDayText = (): string => {
  return dayjs().format('DD');
};

/**
 * 格式化前缀文本
 * @param prefix 前缀
 * @returns 格式化后的前缀
 */
export const formatPrefix = (prefix: string | undefined): string => {
  return prefix?.toUpperCase() || '-';
};

/**
 * 验证流水号长度
 * @param length 长度
 * @returns 是否有效
 */
export const isValidSerialNumberLength = (length: number): boolean => {
  return (
    Number.isInteger(length) &&
    length >= RULE_CONSTANTS.SERIAL_NUMBER.MIN &&
    length <= RULE_CONSTANTS.SERIAL_NUMBER.MAX
  );
};

/**
 * 生成完整的编码示例
 * @param prefix 前缀
 * @param yearType 年份类型
 * @param serialNumberLength 流水号长度
 * @returns 完整的编码示例
 */
export const generateFullCode = (
  prefix: string,
  yearType: number,
  serialNumberLength: number
): string => {
  const formattedPrefix = formatPrefix(prefix);
  const yearText = getYearText(yearType);
  const monthText = getMonthText();
  const dayText = getDayText();
  const serialText = formatNumber(serialNumberLength);

  return `${formattedPrefix}${yearText}${monthText}${dayText}${serialText}`;
};
