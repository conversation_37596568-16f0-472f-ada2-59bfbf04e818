import type { FormInstance } from 'antd/es/form';

// 编码规则相关类型定义
export interface NumberRule {
  number_prefix: string;
  year_type: 1 | 2; // 1: 2位数, 2: 4位数
  month_type: 1; // 1: 2位数
  date_type: 1; // 1: 2位数
  serial_number_digit: string;
}

export interface RuleFormValues {
  prefix: string;
  yearType: 1 | 2;
  monthType: 1;
  dayType: 1;
  serialNumberLength: string | number;
}

export interface ApiResponse<T = unknown> {
  errno: number;
  msg: string;
  data: T;
}

export interface RuleExampleProps {
  prefix: string;
  yearText: string;
  serialNumberText: string;
}

export interface RuleFormProps {
  form: FormInstance<RuleFormValues>;
  loading: boolean;
  onSubmit: () => Promise<void>;
  onValuesChange?: (
    changedValues: Partial<RuleFormValues>,
    allValues: RuleFormValues
  ) => void;
}
