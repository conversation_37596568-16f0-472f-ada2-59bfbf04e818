import React from 'react';
import { Spin, Alert } from 'antd';

interface LoadingStateProps {
  loading: boolean;
  error: string | null;
  children: React.ReactNode;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  loading,
  error,
  children,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Spin size="large" />
          <div className="mt-4 text-gray-500">加载中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <a href="#" onClick={() => window.location.reload()}>
              重新加载
            </a>
          }
        />
      </div>
    );
  }

  return <>{children}</>;
};

export default LoadingState;
