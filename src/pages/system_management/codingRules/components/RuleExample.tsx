import React from 'react';
import { getMonthText, getDayText } from '../config';
import type { RuleExampleProps } from '../types';

const RuleExample: React.FC<RuleExampleProps> = ({
  prefix,
  yearText,
  serialNumberText,
}) => {
  return (
    <div className="rule-example">
      <div className="rule-example-title">编号示例：</div>
      <div className="rule-example-content flex items-center">
        <div className="module">
          <span className="data">{prefix}</span>
          <span className="title">前缀</span>
        </div>
        <div className="module">
          <span className="data">{yearText}</span>
          <span className="title">年份</span>
        </div>
        <div className="module">
          <span className="data">{getMonthText()}</span>
          <span className="title">月份</span>
        </div>
        <div className="module">
          <span className="data">{getDayText()}</span>
          <span className="title">日期</span>
        </div>
        <div className="module">
          <span className="data">{serialNumberText}</span>
          <span className="title">流水号</span>
        </div>
      </div>
    </div>
  );
};

export default RuleExample;
