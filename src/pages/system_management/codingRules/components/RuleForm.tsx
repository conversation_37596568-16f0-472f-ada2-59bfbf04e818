import React from 'react';
import { Form, Input, Radio, Button } from 'antd';
import { RULE_CONSTANTS, VALIDATION_RULES } from '../config';
import type { RuleFormProps } from '../types';

const RuleForm: React.FC<RuleFormProps> = ({
  form,
  loading,
  onSubmit,
  onValuesChange,
}) => {
  return (
    <div className="rule-form mt-[36px] w-[500px]">
      <Form
        form={form}
        labelCol={{ style: { width: 'auto' } }}
        wrapperCol={{ style: { maxWidth: '600px' } }}
        onValuesChange={onValuesChange}
        validateMessages={{
          required: '${label} 为必填项',
          types: { number: '${label} 必须是数字' },
          pattern: { mismatch: '${label} 必须是1-5之间的整数' },
        }}
      >
        <Form.Item
          name="prefix"
          label="编号前缀"
          rules={VALIDATION_RULES.prefix}
        >
          <Input placeholder="请输入编号前缀" />
        </Form.Item>

        <Form.Item
          name="yearType"
          label="年份类型"
          rules={VALIDATION_RULES.yearType}
        >
          <Radio.Group>
            <Radio value={RULE_CONSTANTS.YEAR_TYPES.FOUR_DIGIT}>4位数</Radio>
            <Radio value={RULE_CONSTANTS.YEAR_TYPES.TWO_DIGIT}>2位数</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="monthType"
          label="月份类型"
          rules={VALIDATION_RULES.monthType}
        >
          <Radio.Group>
            <Radio value={RULE_CONSTANTS.DATE_TYPES.TWO_DIGIT}>2位数</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="dayType"
          label="日期类型"
          rules={VALIDATION_RULES.dayType}
        >
          <Radio.Group>
            <Radio value={RULE_CONSTANTS.DATE_TYPES.TWO_DIGIT}>2位数</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="serialNumberLength"
          label="流水号位数"
          rules={VALIDATION_RULES.serialNumberLength}
        >
          <Input
            type="number"
            min={RULE_CONSTANTS.SERIAL_NUMBER.MIN}
            max={RULE_CONSTANTS.SERIAL_NUMBER.MAX}
            placeholder="请输入1-5之间的整数"
          />
        </Form.Item>

        <Form.Item label="" className="mt-[24px]">
          <Button
            type="primary"
            style={{ width: '120px' }}
            onClick={onSubmit}
            loading={loading}
          >
            确定
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default RuleForm;
