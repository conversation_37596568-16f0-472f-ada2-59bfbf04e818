/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';
import './home.scss';
import { getScheduledPeopleNum } from '../../api/home';
import homeYdIcon from '../../assets/images/home-yd-icon.png';

const Home: React.FC = () => {
  const [clientData, setClientData] = useState<any>(null);

  function getClientData() {
    getScheduledPeopleNum().then((res) => {
      console.log(res.data, 23423532);
      setClientData(res.data);
    });
  }

  console.log(clientData, 23423532);

  useEffect(() => {
    getClientData();
  }, []);

  // 数据卡片配置
  const dataCards = [
    {
      title: '当日预定人数',
      value: '12',
      unit: '人',
      icon: homeYdIcon,
      trend: { type: 'up', value: '+10%', text: '较昨日' },
    },
    {
      title: '当日来场人数',
      value: '34',
      unit: '人',
      icon: homeYdIcon,
      trend: { type: 'up', value: '+10%', text: '较昨日' },
    },
    {
      title: '当日会员生日',
      value: '4',
      unit: '人',
      icon: homeYdIcon,
      trend: null,
    },
    {
      title: '当日会员到期人数',
      value: '7',
      unit: '人',
      icon: homeYdIcon,
      trend: null,
    },
  ];
  console.log(dataCards, 5652);

  return (
    <div className="home-container h-full w-full ">
      {/* 数据统计卡片 - 移动优先的响应式设计 */}
      <div className="home-header h-[8rem] mb-4">
        {/* 
          简洁的响应式网格：
          - 默认：2列（移动端）
          - sm以上：2列（大手机/小平板）
          - lg以上：4列（桌面端）
        */}
      </div>

      <div className="home-echart-box"></div>
    </div>
  );
};

export default Home;
