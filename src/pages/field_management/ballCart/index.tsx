import { Table } from 'antd';
import React from 'react';

const { Column } = Table;

const BallCart: React.FC = () => {
  return <div className='ball-cart-container h-full w-full'>
    <div className='tool-box flex items-center justify-between'>
      <div className='tool'>
        <span>新增球车</span>
      </div>
    </div>
    <div className='table-box mt-4 h-[calc(100%-3.5rem)] bg-white p-4'>
    <Table
          rowKey="user_id"
          dataSource={[]}
          loading={false}
          pagination={false}
          scroll={{ y: 'auto' }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
        >
              <Column title="Age" dataIndex="age" key="age" />
              <Column title="Address" dataIndex="address" key="address" />
          </Table>
    </div>

  </div>;
};

export default BallCart;
