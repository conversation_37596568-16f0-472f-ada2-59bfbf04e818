import { Table, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React from 'react';
import type { ColumnsType } from 'antd/es/table';

// 示例数据类型
interface BallCartData {
  id: string;
  cartNumber: string;
  status: string;
  batteryLevel: number;
  location: string;
}

const BallCart: React.FC = () => {
  // 示例数据
  const mockData: BallCartData[] = [
    {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },

        {
      id: '1',
      cartNumber: 'BC001',
      status: '可用',
      batteryLevel: 85,
      location: '1号发球台',
    },
    {
      id: '2',
      cartNumber: 'BC002',
      status: '使用中',
      batteryLevel: 60,
      location: '3号球道',
    },
  ];

  // 表格列配置
  const columns: ColumnsType<BallCartData> = [
    {
      title: '球车编号',
      dataIndex: 'cartNumber',
      key: 'cartNumber',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
    },
    {
      title: '电量',
      dataIndex: 'batteryLevel',
      key: 'batteryLevel',
      width: 100,
      render: (value: number) => `${value}%`,
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: () => (
        <div className="flex gap-2">
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small" danger>删除</Button>
        </div>
      ),
    },
  ];

  return (
    <div className="ball-cart-container h-full w-full flex flex-col">
      {/* 工具栏 */}
      <div className="tool-box flex items-center justify-between p-4 bg-white">
        <div className="tool">
          <Button type="primary" icon={<PlusOutlined />}>
            新增球车
          </Button>
        </div>
      </div>

      {/* 表格容器 */}
      <div className="table-box flex-1 bg-white mx-4 mb-4 overflow-hidden">
        <Table<BallCartData>
          rowKey="id"
          dataSource={mockData}
          columns={columns}
          loading={false}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            defaultPageSize: 20,
          }}
          scroll={{
            y: 'calc(100vh - 200px)', // 动态计算高度
            x: 'max-content' // 水平滚动
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          size="middle"
        />
      </div>
    </div>
  );
};

export default BallCart;
