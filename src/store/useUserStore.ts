/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand';
import { getMenu, getUserInfo, login } from '../api/login';
import { getToken, removeToken, setToken } from '../utils/auth';
import { getClientInfo, getStylePartitions } from '../api/client';

interface LoginInfo {
  username: string;
  password: string;
  clientId: number;
}

interface LoginResponse {
  data: {
    access_token: string;
  };
}

interface ClientInfo {
  id: number;
  roles?: string[];
  [key: string]: unknown;
}

interface PartInfo {
  id: number;
  part_name: string;
}

interface Partition {
  part_info: PartInfo[];
}

interface UserStoreState {
  token: string;
  id: string;
  roles: string[];
  permissions: string[];
  user: Record<string, unknown>;
  clientInfo: ClientInfo;
  partitions: Partition[];
  menuList: any[];
  login: (userInfo: LoginInfo) => Promise<void>;
  getInfo: () => Promise<unknown>;
  logOut: () => Promise<void>;
}

const useUserStore = create<UserStoreState>((set) => ({
  token: getToken() || '',
  id: '',
  roles: [],
  permissions: [],
  user: {},
  clientInfo: {} as ClientInfo,
  partitions: [],
  menuList: [],
  // 登录
  async login(userInfo: LoginInfo) {
    const username = userInfo.username.trim();
    const password = userInfo.password;
    const clientId = userInfo.clientId;
    const res: LoginResponse = await login(username, password, clientId);
    setToken(res.data.access_token);
    set({ token: res.data.access_token });
  },
  // 获取用户信息
  async getInfo() {
    const menu = await getMenu();
    const res = await getUserInfo();
    const clientInfo = await getClientInfo(res.data.client_id);
    const partitions = await getStylePartitions();

    set({
      clientInfo: clientInfo.data,
      partitions: partitions.data,
      user: res.data,
      menuList: menu.data,
    });
    return res;
  },
  // 退出系统
  async logOut() {
    set({
      token: '',
      roles: [],
      permissions: [],
    });
    removeToken();
  },
}));

export default useUserStore;
