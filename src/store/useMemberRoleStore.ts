/* eslint-disable @typescript-eslint/no-explicit-any */
// stores/memberRoleStore.ts
import { create } from 'zustand';
import { getSetMealListByAllRole } from '../api/reservation';
import { getOtherConfig } from '../api/system';

interface Role {
  id: number | string;
  name: string;
  [key: string]: any; // 其他可能的属性
}

interface SysConfig {
  role?: string | number;
  teamRole?: string | number;
  vipRole?: string | number;
  searchRole?: string | number;
  roleConfig?: Array<{ name: string }>;
  CommonPhrases?: Array<{ label: string }>;
  [key: string]: any; // 其他可能的配置属性
}

interface MemberRoleState {
  memberRole: Role[];
  currentSelectedRowData: Record<string, any>;
  sysConfig: SysConfig | null;

  // 动作类型
  getMemberRole: (data?: any) => Promise<void>;
  setCurrentSelectedRowData: (data: any) => void;
  getOtherConfig: () => Promise<void>;
}

const useMemberRoleStore = create<MemberRoleState>((set) => ({
  memberRole: [],
  currentSelectedRowData: {},
  sysConfig: null,

  // 获取会员角色
  getMemberRole: async (data?: any) => {
    try {
      const res = await getSetMealListByAllRole(data);
      set({ memberRole: res.data });
    } catch (error) {
      console.error('获取会员角色失败:', error);
      set({ memberRole: [] });
    }
  },

  // 设置当前选中的行数据
  setCurrentSelectedRowData: (data: any) => {
    set({ currentSelectedRowData: data });
  },

  // 获取其他配置
  getOtherConfig: async () => {
    try {
      const res = await getOtherConfig();
      const config = JSON.parse(res.data.sys_config || '{}');
      set({ sysConfig: config });
    } catch (error) {
      console.error('解析系统配置失败:', error);
      set({ sysConfig: null });
    }
  },
}));

export default useMemberRoleStore;
