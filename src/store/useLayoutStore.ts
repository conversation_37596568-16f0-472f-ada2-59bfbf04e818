import { create } from 'zustand';

interface LayoutStoreState {
  collapsed: boolean;
  isMobile: boolean;
  setCollapsed: (collapsed: boolean) => void;
  setIsMobile: (isMobile: boolean) => void;
}

const useLayoutStore = create<LayoutStoreState>((set) => ({
  collapsed: false,
  isMobile: false,
  setCollapsed: (collapsed) => set({ collapsed }),
  setIsMobile: (isMobile) => set({ isMobile }),
}));

export default useLayoutStore;
