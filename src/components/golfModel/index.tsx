import React from 'react';
import { Button, Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

interface GolfModelProps {
  title?: React.ReactNode;
  open: boolean;
  onCancel?: () => void;
  onOk?: () => void;
  footer?: React.ReactNode;
  closable?: boolean;
  children?: React.ReactNode;
  [key: string]: unknown;
}

const GolfModel: React.FC<GolfModelProps> = ({
  title,
  open,
  onCancel,
  onOk,
  children,
  ...rest
}) => {
  return (
    <Modal
      title={
        <div className="h-[3rem] flex items-center justify-between bg-[#6280f5] px-4 text-white">
          <span></span>
          <span style={{ fontWeight: 400, color: '#fff' }}>{title}</span>
          <CloseOutlined
            style={{
              fontSize: 20,
              color: '#fff',
              cursor: 'pointer',
              fontWeight: 400,
            }}
            onClick={onCancel}
          />
        </div>
      }
      centered
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      footer={false}
      closable={false}
      {...rest}
    >
      {children}

      <div className="w-full h-[3.5rem] flex items-center justify-center gap-4">
        <Button type="primary" onClick={onOk}>
          确定
        </Button>
        <Button onClick={onCancel}>取消</Button>
      </div>
    </Modal>
  );
};

export default GolfModel;
