import request from '../utils/request';

// 登录方法
export function login(username: string, password: string, clientId: number) {
  const data = {
    admin_name: username,
    password,
    client_id: clientId,
  };
  return request({
    url: 'user/login',
    headers: {
      isToken: false,
    },
    method: 'post',
    data: data,
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: 'user/me',
    headers: {
      isToken: true,
      urlPrefix: 'v2/admin',
    },
    method: 'get',
  });
}

// 获取菜单
export function getMenu() {
  return request({
    url: 'menu',
    headers: {
      isToken: true,
    },
    method: 'get',
  });
}
