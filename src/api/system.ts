/* eslint-disable @typescript-eslint/no-explicit-any */
import request from '../utils/request';

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/getRole',
    method: 'get',
  });
}

//删除角色
export function deleteRole(data: any) {
  return request({
    url: '/deleteRole',
    method: 'post',
    data,
  });
}

//添加角色
export function setAddRole(data: any) {
  return request({
    url: '/addRole',
    method: 'post',
    data,
  });
}

//获取用户列表
export function getUserList() {
  return request({
    url: '/user/list',
    method: 'get',
  });
}

//删除用户
export function setUserDelete(data: any) {
  return request({
    url: '/user/delete',
    method: 'post',
    data,
  });
}

//修改密码
export function setUserPassword(data: any) {
  return request({
    url: 'updatePassword',
    method: 'post',
    data,
  });
}

//新增用户
export function setUserStore(data: any) {
  return request({
    url: 'user/store',
    method: 'post',
    data,
  });
}

//消费卡管理
export function getConsumeCardList(data: any) {
  return request({
    url: '/clientsPassports',
    method: 'get',
    params: data,
  });
}

//新增编辑消费卡
export function setConsumeCardStore(data: any) {
  return request({
    url: '/clientsPassports',
    method: 'post',
    data,
  });
}

//删除消费卡
export function setConsumeCardDelete(id: any) {
  return request({
    url: `/clientsPassports/${id}`,
    method: 'delete',
  });
}

//批量新增消费卡
export function batchSetConsumeCardStore(data: any) {
  return request({
    url: 'clientsPassports/batch',
    method: 'post',
    data,
  });
}

//根据序列号获取消费卡信息
export function getConsumeCardInfo(data: any) {
  return request({
    url: '/selectClientsPassports',
    method: 'get',
    params: data,
  });
}

//封场列表
export function getClosureList(data: any) {
  return request({
    url: '/getClosureSchemesList',
    method: 'get',
    params: data,
  });
}

//变更状态
export function setClosureStatus(id: any, data: any) {
  return request({
    url: `/toggleClosureScheme/${id}`,
    method: 'post',
    data,
  });
}

//删除封场
export function setClosureDelete(data: any) {
  return request({
    url: `/delClosureScheme`,
    method: 'DELETE',
    data,
  });
}

//新增封场
export function saveClosureSchemeAPI(data: any) {
  return request({
    url: '/saveClosureScheme',
    method: 'post',
    data,
  });
}

//设置其他设置
export function setOtherConfig(data: any) {
  return request({
    url: '/client/other/setting',
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
    data,
  });
}

//获取其他设置
export function getOtherConfig() {
  return request({
    url: '/client/other/setting',
    method: 'get',
    headers: {
      urlPrefix: 'v2/admin',
    },
  });
}
//获取编码规则
export function getNumberRule() {
  return request({
    url: '/detailNumberRule',
    method: 'get',
    headers: {
      urlPrefix: 'v2/admin',
    },
  });
}

//更新编码规则
export function updateNumberRule(data: any) {
  return request({
    url: '/updateNumberRule',
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
    data,
  });
}
//获取营业点列表
export function pageBusinessPoint(data: any) {
  return request({
    url: '/pageBusinessPoint',
    method: 'get',
    headers: {
      urlPrefix: 'v2/admin',
    },
    params: data,
  });
}
//新增营业点
export function insertBusinessPoint(data: any) {
  return request({
    url: '/insertBusinessPoint',
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
    data,
  });
}
//编辑营业点
export function updateBusinessPoint(data: any) {
  return request({
    url: '/updateBusinessPoint',
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
    data,
  });
}

//删除营业点
export function deleteBusinessPoint(data: any) {
  return request({
    url: `/deleteBusinessPoint?id=${data.id}`,
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
  });
}
//停用启用营业厅
export function updateBusinessPointStatus(data: any) {
  return request({
    url: `/updateBusinessPointStatus?id=${data.id}&status=${data.status}`,
    method: 'post',
    headers: {
      urlPrefix: 'v2/admin',
    },
  });
}
