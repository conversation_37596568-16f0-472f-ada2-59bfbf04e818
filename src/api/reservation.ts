import request from '../utils/request';

// 获取预约列表
export function getTeeTimeAppointmentListAPI(data: unknown) {
  return request({
    url: '/getTeeTimeAppointmentList',
    method: 'get',
    params: data,
  });
}

//获取日期类型
export function getDayType(data: unknown) {
  return request({
    url: '/getDayType',
    method: 'get',
    params: data,
  });
}

export function setMarkColorAPI(data: unknown) {
  return request({
    url: '/setMarkColor',
    method: 'post',
    data,
  });
}

// 获取搜索框数据
export function getSearchDataAPI(data: unknown) {
  return request({
    url: '/appointmentSearch',
    method: 'get',
    params: data,
  });
}

//标记整组颜色
export function setBatchMarkColor(data: unknown) {
  return request({
    url: '/setBatchMarkColor',
    method: 'post',
    data,
  });
}

//获取身份
export function getSetMealListByAllRole(data: unknown) {
  return request({
    url: '/getSetMealListByAllRole',
    method: 'get',
    params: data,
  });
}
//获取销售员列表信息
export function getClientSalesperson() {
  return request({
    url: '/getClientSalesperson',
    method: 'get',
  });
}

//获取转场分区及转场时间
export function getTransPartitionAndTeeTime(data: unknown) {
  return request({
    url: '/transPartition',
    method: 'get',
    params: data,
  });
}

//获取会员信息
export function getMemberInfo(data: unknown) {
  return request({
    url: '/screenVipInfo',
    method: 'get',
    params: data,
  });
}

//新增预订
export function addReservation(data: unknown) {
  return request({
    url: '/create/appointment',
    method: 'post',
    data,
  });
}

//取消预订
export function cancelReservation(data: unknown) {
  return request({
    url: '/minusHit',
    method: 'post',
    data,
  });
}

export function getAppointmentTeeTimeList(data: unknown) {
  return request({
    url: '/getAppointmentTeeTimeList',
    method: 'get',
    params: data,
  });
}

//粘贴预订
export function copyCutshearPersonal(data: unknown) {
  return request({
    url: '/shearPersonal',
    method: 'post',
    data,
  });
}

//获取分区teeTime
export function getPartitionTeeTime(data: unknown) {
  return request({
    url: '/getPartitionTeeTime',
    method: 'get',
    params: data,
  });
}

//加打
export function addPlayBall(data: unknown) {
  return request({
    url: '/addPlayBall',
    method: 'post',
    data,
  });
}

//取消加打
export function cancelOpenCard(data: unknown) {
  return request({
    url: '/cancelOpenCard',
    method: 'post',
    data,
  });
}

//分配球童球车
export function setAllotCaddieCarAdd(data: unknown) {
  return request({
    url: '/allotCaddieCarAdd',
    method: 'post',
    data,
  });
}

//获取出发球洞
export function getSetOutHoles(data: unknown) {
  return request({
    url: '/getSetOutHoles',
    method: 'get',
    params: data,
  });
}

//取消出发
export function cancelOut(data: unknown) {
  return request({
    url: '/cancelOut',
    method: 'post',
    data,
  });
}

//更新球童信息
export function appointmentUpCaddie(data: unknown) {
  return request({
    url: '/appointmentUpCaddie',
    method: 'post',
    data,
  });
}

//查询人员当日预约次数
export function getReservationCount(data: unknown) {
  return request({
    url: '/getTodayAppointmentCount',
    method: 'get',
    params: data,
  });
}

//散团预定分配tee time
export function getDistributeTeeTime(data: unknown) {
  return request({
    url: '/distributeTeeTime',
    method: 'get',
    params: data,
  });
}

// tee time统计报表导出
export function teeTimeAppointmentExport(data: unknown) {
  return request({
    url: '/teeTimeAppointmentExport',
    method: 'get',
    responseType: 'blob',
    params: data,
  });
}

//获取当前月份的天数
export function getDaysInMonthApi(data: unknown) {
  return request({
    url: '/getAppointmentDate',
    method: 'get',
    params: data,
  });
}

//汇总统计
export function getgather(data: unknown) {
  return request({
    url: '/statistic/gather',
    method: 'get',
    params: data,
  });
}

//按状态
export function getStatisticstatus(data: unknown) {
  return request({
    url: '/statistic/status',
    method: 'get',
    params: data,
  });
}

//按分区
export function getStatisticpartition(data: unknown) {
  return request({
    url: '/statistic/partition',
    method: 'get',
    params: data,
  });
}

//按性别
export function getStatisticsex(data: unknown) {
  return request({
    url: '/statistic/sex',
    method: 'get',
    params: data,
  });
}

//按钟点
export function getreportByHour(data: unknown) {
  return request({
    url: '/reportByHour',
    method: 'get',
    params: data,
  });
}

//按身份
export function getreportByCard(data: unknown) {
  return request({
    url: '/reportByCard',
    method: 'get',
    params: data,
  });
}

//来场报表
export function getreportByLaiChang(data: unknown) {
  return request({
    url: '/reportByLaiChang',
    method: 'get',
    params: data,
  });
}

//国籍报表
export function getreportByNationality(data: unknown) {
  return request({
    url: '/reportByNationality',
    method: 'get',
    params: data,
  });
}

//十二点数据统计
export function getreportByTeetime(data: unknown) {
  return request({
    url: '/reportByTeetime',
    method: 'get',
    params: data,
  });
}

//团体预定
export function getreportByGroupReserve(data: unknown) {
  return request({
    url: '/reportByGroupReserve',
    method: 'get',
    params: data,
  });
}

//打球洞数统计
export function getreportByHoleNum(data: unknown) {
  return request({
    url: '/reportByHoleNum',
    method: 'get',
    params: data,
  });
}

//套餐数据统计
export function getreportBySetMeal(data: unknown) {
  return request({
    url: '/reportBySetMeal',
    method: 'get',
    params: data,
  });
}

//人员结束球局
export function endPersonBall(data: unknown) {
  return request({
    url: '/endPersonBall',
    method: 'post',
    data,
  });
}

//修改teetime
export function updateOutTime(data: unknown) {
  return request({
    url: '/updateOutTime',
    method: 'post',
    data,
  });
}

//散团获取转场分区及转场时间
export function teamTransPartition(data: unknown) {
  return request({
    url: '/teamTransPartition',
    method: 'get',
    params: data,
  });
}

//散团预订取消
export function cancleTeamAppointMent(data: unknown) {
  return request({
    url: '/cancleTeamAppointMent',
    method: 'post',
    data,
  });
}

// 新增自定义teetime
export function addCustomizeTeetime(data: unknown) {
  return request({
    url: '/addCustomizeTeetime',
    method: 'post',
    data,
  });
}
