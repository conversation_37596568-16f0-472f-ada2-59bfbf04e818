import { App } from 'antd';

/**
 * 自定义message hook，用于替代静态方法
 * 解决Antd 5中静态方法无法消费Context的问题
 */
export const useMessage = () => {
  const { message } = App.useApp();
  return message;
};

/**
 * 自定义modal hook
 */
export const useModal = () => {
  const { modal } = App.useApp();
  return modal;
};

/**
 * 自定义notification hook
 */
export const useNotification = () => {
  const { notification } = App.useApp();
  return notification;
};

export default useMessage;
