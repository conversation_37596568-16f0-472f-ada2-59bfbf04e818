const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 1. 生成版本号 golf+年月日时分秒
const now = new Date();
const pad = n => n.toString().padStart(2, '0');
const version = `golf${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;

// 2. 打 git tag
try {
  execSync(`git tag ${version}`);
  execSync(`git push origin ${version}`);
  console.log(`Git tag created: ${version}`);
} catch (e) {
  console.error('Git tag failed:', e.message);
}

// 3. 写入 index.html meta
const indexPath = path.resolve(__dirname, '../index.html');
let html = fs.readFileSync(indexPath, 'utf-8');
const metaTag = `<meta name="golf-version" content="${version}">`;

if (html.includes('name="golf-version"')) {
  html = html.replace(/<meta name="golf-version" content=".*?">/, metaTag);
} else {
  html = html.replace(/<head>/, `<head>\n  ${metaTag}`);
}
fs.writeFileSync(indexPath, html, 'utf-8');
console.log('Version meta injected to index.html:', version); 