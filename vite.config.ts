import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  // 根据环境设置 base 路径
  base: mode === 'production' ? './' : '/',
  server: {
    port: 3000,
    host: true,
  },
  // 构建配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    // 生成 source map 用于调试
    sourcemap: mode !== 'production',
  },
  // 开发模式优化
  esbuild: {
    // 减少重复构建
    keepNames: true,
  },
  optimizeDeps: {
    // 预构建依赖，减少运行时重复加载
    include: ['react', 'react-dom', 'antd'],
  },
}))
