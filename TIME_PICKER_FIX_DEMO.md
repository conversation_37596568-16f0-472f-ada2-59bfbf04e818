# TimePicker.RangePicker Invalid Date 问题修复

## 问题分析

你遇到的问题是在 `TimePicker.RangePicker` 组件中显示 "Invalid Date"，主要原因是：

1. **时间格式不匹配**：你使用 `'HH:mm:ss'` 格式解析时间，但 `TimePicker.RangePicker` 的 `format` 设置为 `"HH:mm"`
2. **时间解析失败**：dayjs 无法正确解析时间字符串，导致返回无效的日期对象

## 原始问题代码

```typescript
// 问题代码
const startTime = dayjs('12:08:23', 'HH:mm:ss');
const endTime = dayjs('12:08:23', 'HH:mm:ss');

// TimePicker.RangePicker 配置
<TimePicker.RangePicker 
  format="HH:mm"  // 这里格式是 HH:mm
  style={{ width: '100%' }} 
/>
```

## 解决方案

### 1. 创建时间解析工具函数

```typescript
// src/utils/timeUtils.ts
import dayjs, { Dayjs } from 'dayjs';

export const parseTimeString = (timeStr: string): Dayjs | undefined => {
  if (!timeStr) return undefined;
  
  // 尝试不同的时间格式
  const formats = ['HH:mm:ss', 'HH:mm', 'H:mm:ss', 'H:mm'];
  
  for (const format of formats) {
    const parsed = dayjs(timeStr, format);
    if (parsed.isValid()) {
      return parsed;
    }
  }
  
  // 如果都解析失败，尝试直接解析
  const directParsed = dayjs(timeStr);
  return directParsed.isValid() ? directParsed : undefined;
};

export const createTimeRange = (startTime: string, endTime: string): [Dayjs, Dayjs] | undefined => {
  const start = parseTimeString(startTime);
  const end = parseTimeString(endTime);
  
  if (start && end && start.isValid() && end.isValid()) {
    return [start, end];
  }
  
  return undefined;
};
```

### 2. 在组件中使用工具函数

```typescript
// 修复后的代码
import { createTimeRange } from '../../../../utils/timeUtils';

// 在 useEffect 中
const timeRange = createTimeRange(editingRecord.start_time, editingRecord.end_time);

const formData: Partial<FormClosureData> = {
  // ... 其他字段
  time: timeRange, // 直接使用解析后的时间范围
};
```

### 3. 完整的组件示例

```typescript
const ClosureModal: React.FC<ClosureModalProps> = ({ ... }) => {
  useEffect(() => {
    if (open && isEditing && editingRecord) {
      // 使用工具函数创建时间范围
      const timeRange = createTimeRange(editingRecord.start_time, editingRecord.end_time);
      
      const formData = {
        ...editingRecord,
        time: timeRange, // 确保时间格式正确
        effective_date_start: editingRecord.effective_date_start ? 
          dayjs(editingRecord.effective_date_start) : undefined,
        partition_ids: editingRecord.partition_ids ? 
          editingRecord.partition_ids.split(',').map(Number) : [],
      };
      
      form.setFieldsValue(formData);
    }
  }, [open, isEditing, editingRecord, form]);

  return (
    <GolfModel>
      <Form form={form}>
        <Form.Item
          label="封场时间范围"
          name="time"
          rules={[{ required: true, message: '请选择封场时间范围' }]}
        >
          <TimePicker.RangePicker 
            format="HH:mm" 
            style={{ width: '100%' }} 
          />
        </Form.Item>
      </Form>
    </GolfModel>
  );
};
```

## 关键修复点

1. **统一时间格式**：确保解析时间时考虑多种格式
2. **验证时间有效性**：在设置表单值之前验证时间是否有效
3. **错误处理**：当时间解析失败时返回 undefined，避免设置无效值
4. **工具函数复用**：创建可复用的时间处理工具函数

## 测试验证

```typescript
// 测试不同时间格式
console.log(createTimeRange('12:08:23', '13:30:45')); // 应该正常工作
console.log(createTimeRange('12:08', '13:30'));       // 应该正常工作
console.log(createTimeRange('9:08', '17:30'));        // 应该正常工作
console.log(createTimeRange('invalid', '13:30'));     // 应该返回 undefined
```

## 最佳实践

1. **时间格式一致性**：确保解析格式与显示格式匹配
2. **错误边界**：始终验证时间解析结果
3. **工具函数**：将时间处理逻辑抽象为可复用的工具函数
4. **类型安全**：使用 TypeScript 确保类型安全

这样修复后，`TimePicker.RangePicker` 就不会再显示 "Invalid Date" 了。
